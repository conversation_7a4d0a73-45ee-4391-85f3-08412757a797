//
// Layout
// Note: Must name this file "outline" instead of "layout"
//       Or <PERSON><PERSON><PERSON> will use it as template layout.
// =================================================


html, body { height: 100%; }

.container {
  position: relative;
}


// Header Section
// --------------------------------------------------
.header-inner {
  margin: 0 auto;
  padding: 100px 0 70px;
  width: $content-desktop;

  +desktop-large() {
    .container & { width: $content-desktop-large; }
  }
  +desktop-largest() {
    .container & { width: $content-desktop-largest; }
  }
}


// Main Section
// --------------------------------------------------
.main-inner {
  margin: 0 auto;
  width: $content-desktop;

  +desktop-large() {
    .container & { width: $content-desktop-large; }
  }
  +desktop-largest() {
    .container & { width: $content-desktop-largest; }
  }
}


// Footer Section
// --------------------------------------------------
.footer {
  padding: 20px 0;
}
.footer-inner {
  box-sizing: border-box;
  margin: 0px auto;
  width: $content-desktop;

  +desktop-large() {
    .container & { width: $content-desktop-large; }
  }
  +desktop-largest() {
    .container & { width: $content-desktop-largest; }
  }
}
