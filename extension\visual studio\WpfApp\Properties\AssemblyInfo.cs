﻿using System.Reflection;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("HandyControl VS Templates WpfApp")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("HandyControl VS Templates WpfApp")]
[assembly: AssemblyCopyright("Copyright © HandyOrg 2018-2021")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: ComVisible(false)]
[assembly: AssemblyVersion("3.3.0")]
[assembly: AssemblyFileVersion("3.3.0")]
