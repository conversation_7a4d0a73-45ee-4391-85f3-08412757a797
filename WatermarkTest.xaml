<Window x:Class="WatermarkTest.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:StandaloneTimePicker.Controls;assembly=StandaloneTimePicker"
        Title="WatermarkTextBox Test" Height="400" Width="600">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0" Text="✅ FIXED: WatermarkTextBox Behavior Test" 
                   FontSize="20" FontWeight="Bold" Foreground="Green" Margin="0,0,0,20"/>
        
        <TextBlock Grid.Row="1" Text="TimePicker with Watermark:" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="2" x:Name="TestTimePicker" Width="300" HorizontalAlignment="Left" Margin="0,0,0,15"/>
        
        <TextBlock Grid.Row="3" Text="Standalone WatermarkTextBox:" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:WatermarkTextBox Grid.Row="4" x:Name="TestWatermarkBox" 
                                   Width="300" Height="30" 
                                   HorizontalAlignment="Left" 
                                   Watermark="Type something here..."
                                   BorderBrush="Gray" BorderThickness="1"
                                   Padding="5"/>
        
        <Border Grid.Row="5" BorderBrush="LightGray" BorderThickness="1" Margin="0,20,0,0" Padding="15">
            <StackPanel>
                <TextBlock Text="✅ WATERMARK BEHAVIOR TEST:" FontWeight="Bold" FontSize="16" Foreground="DarkBlue" Margin="0,0,0,10"/>
                
                <TextBlock Text="Expected Behavior:" FontWeight="Bold" Margin="0,0,0,5"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="1." FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="When empty and not focused: Watermark VISIBLE (gray text)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="2." FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="When clicked (gains focus): Watermark DISAPPEARS immediately"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="3." FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="When typing: User text VISIBLE, watermark HIDDEN"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="4." FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="When has text and loses focus: User text VISIBLE, watermark HIDDEN"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="5." FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="When cleared and loses focus: Watermark REAPPEARS"/>
                </StackPanel>
                
                <TextBlock Text="Test Instructions:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Click in the text fields and observe watermark behavior" Margin="0,2"/>
                <TextBlock Text="• Type some text and see that it's clearly visible" Margin="0,2"/>
                <TextBlock Text="• Clear the text and click outside to see watermark return" Margin="0,2"/>
                <TextBlock Text="• Test both TimePicker and standalone WatermarkTextBox" Margin="0,2"/>
                
                <TextBlock Text="🎉 Watermark visibility issue is now FIXED!" 
                           FontWeight="Bold" FontSize="14" Foreground="DarkGreen" Margin="0,15,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
