﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:views="clr-namespace:HandyControlDemo.Views"
             mc:Ignorable="d"
             d:DesignWidth="800"
             d:DesignHeight="450"
             Background="{StaticResource CloudDrawingBrush}"
             x:Class="HandyControlDemo.Views.MainWindowContent">
	<Grid MinHeight="300" MinWidth="200" HorizontalAlignment="Center" VerticalAlignment="Center">
		<Grid.RowDefinitions>
			<RowDefinition Height="Auto"/>
			<RowDefinition/>
		</Grid.RowDefinitions>
		<Border Grid.RowSpan="2" CornerRadius="10" Background="{DynamicResource RegionBrush}" Effect="{StaticResource EffectShadow4}"/>
		<Border Grid.Row="0" Height="32" CornerRadius="10 10 0 0" Background="{DynamicResource TitleBrush}">
            <TextBlock Text="Button" Foreground="{DynamicResource TextIconBrush}" VerticalAlignment="Center" HorizontalAlignment="Center"/>
        </Border>
		<views:ButtonDemoCtrl Grid.Row="1"/>
	</Grid>
</UserControl>
