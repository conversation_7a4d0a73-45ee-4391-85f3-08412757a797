.post-body {
  word-wrap();
  font-family: $font-family-posts;
}

.post-body span.exturl {
  .fa {
    font-size: 14px;
    margin-left: 4px;
  }
}

.post-body .fancybox img {
  display: block !important;
  margin: 0 auto;
  cursor: pointer;
  cursor: zoom-in;
}

.post-body .image-caption,
.post-body .figure .caption {
  margin: -20px auto 15px;
  text-align: center;
  font-size: $font-size-base;
  color: $grey-dark;
  font-weight: bold;
  line-height: 1;
}

.post-sticky-flag {
  display: inline-block;
  font-size: 16px;
  transform: rotate(30deg);
}

.use-motion {
  if hexo-config('motion.transition.post_block') {
    .post-block,
    .pagination,
    .comments { opacity: 0; }
  }
  if hexo-config('motion.transition.post_header') { .post-header { opacity: 0; } }
  if hexo-config('motion.transition.post_body') { .post-body { opacity: 0; } }
  if hexo-config('motion.transition.coll_header') { .collection-title { opacity: 0; } }
}

@import "post-expand";
@import "post-collapse";
@import "post-type";
@import "post-title";
@import "post-meta";
@import "post-button";
@import "post-tags";
@import "post-nav";
@import "post-eof";
@import "post-gallery";
@import "post-reward" if hexo-config('reward_settings.enable');
@import "post-copyright" if hexo-config('creative_commons.post');
@import "post-widgets" if (hexo-config('facebook_sdk.enable') and hexo-config('facebook_sdk.like_button')) or (hexo-config('vkontakte_api.enable') and hexo-config('vkontakte_api.like')) or hexo-config('rating.enable') or hexo-config('likely.enable') or (hexo-config('needmoreshare2.enable') and hexo-config('needmoreshare2.postbottom.enable')) or hexo-config('baidushare');
@import "post-rtl";
@import "post-reading_progress" if hexo-config('reading_progress.enable');
