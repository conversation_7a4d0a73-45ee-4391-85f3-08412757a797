---
title: 基础xaml定义
---

基础xaml定义都在Basic文件夹中，其内部结构如下：
![Basic_Structure](https://raw.githubusercontent.com/HandyOrg/HandyOrgResource/master/HandyControl/Doc/basic_xaml/Basic_Structure.png)

对它们的描述如下表所示：

| 名称 | 用途 |
|-|-|
| Colors | 包含内置的3个颜色定义文件 |
| Basic.xaml | 包装好的基础xaml定义 |
| Behaviors.xaml | 包含所有的行为定义 |
| Brushes.xaml | 包含所有的画刷定义 |
| Converters.xaml | 包含所有的转换器定义 |
| Effects.xaml | 包含所有的效果定义 |
| Fonts.xaml | 包含所有的字体大小定义 |
| Geometries.xaml | 包含所有的几何形状定义，多用于Path |
| Paths.xaml | 包含所有的Path路径定义 |
| Sizes.xaml | 包含所有的尺寸定义 |