﻿<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
	<Styles.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <MergeResourceInclude Source="avares://HandyControl/Themes/Basic/Brushes.axaml" />
                <MergeResourceInclude Source="avares://HandyControl/Themes/Styles/ContentControl.axaml" />
                <MergeResourceInclude Source="avares://HandyControl/Themes/Styles/UserControl.axaml" />
                <MergeResourceInclude Source="avares://HandyControl/Themes/Styles/Window.axaml" />
                <MergeResourceInclude Source="avares://HandyControl/Themes/Styles/Button.axaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Styles.Resources>
    <Style Selector=":is(UserControl)">
        <Setter Property="Theme" Value="{DynamicResource {x:Type UserControl}}" />
    </Style>
</Styles>
