﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>HandyControl.Lang.{lang}</id>
    <version>{version}</version>
    <title>HandyOrg</title>
    <authors>HandyOrg</authors>
    <owners>HandyOrg</owners>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <license type="file">LICENSE</license>
    <icon>icon.png</icon>
    <projectUrl>https://github.com/HandyOrg/HandyControl</projectUrl>
    <description>Contains some simple and commonly used WPF controls</description>
    <releaseNotes>Changes are detailed at https://github.com/HandyOrg/HandyControl/releases</releaseNotes>
    <copyright>Copyright © HandyOrg 2018-{year}</copyright>
    <tags>WPF C# Control</tags>
    <repository type="git" url="https://github.com/HandyOrg/HandyControl" commit="{commit}" />
    <dependencies />
  </metadata>
  <files>
    <file src="..\..\icon.png" target="icon.png" />
    <file src="..\..\..\LICENSE" target="LICENSE" />
  </files>
</package>
