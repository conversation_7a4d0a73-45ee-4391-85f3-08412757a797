﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="using:System">
    <ResourceDictionary.ThemeDictionaries>
        <ResourceDictionary x:Key="Default">
            <Color x:Key="LightPrimaryColor">#f3fbff</Color>
            <Color x:Key="PrimaryColor">#326cf3</Color>
            <Color x:Key="DarkPrimaryColor">#326cf3</Color>

            <Color x:Key="LightDangerColor">#fff6f7</Color>
            <Color x:Key="DangerColor">#db3340</Color>
            <Color x:Key="DarkDangerColor">#db3340</Color>

            <Color x:Key="LightWarningColor">#fffcf5</Color>
            <Color x:Key="WarningColor">#e9af20</Color>
            <Color x:Key="DarkWarningColor">#e9af20</Color>

            <Color x:Key="LightInfoColor">#f1fdff</Color>
            <Color x:Key="InfoColor">#00bcd4</Color>
            <Color x:Key="DarkInfoColor">#00bcd4</Color>

            <Color x:Key="LightSuccessColor">#f3fff6</Color>
            <Color x:Key="SuccessColor">#2db84d</Color>
            <Color x:Key="DarkSuccessColor">#2db84d</Color>

            <Color x:Key="PrimaryTextColor">#212121</Color>
            <Color x:Key="SecondaryTextColor">#757575</Color>
            <Color x:Key="ThirdlyTextColor">#bdbdbd</Color>
            <Color x:Key="ReverseTextColor">#212121</Color>
            <Color x:Key="TextIconColor">White</Color>

            <Color x:Key="BorderColor">#e0e0e0</Color>
            <Color x:Key="SecondaryBorderColor">#757575</Color>
            <Color x:Key="BackgroundColor">#eeeeee</Color>
            <Color x:Key="RegionColor">#ffffff</Color>
            <Color x:Key="SecondaryRegionColor">#eeeeee</Color>
            <Color x:Key="ThirdlyRegionColor">White</Color>
            <Color x:Key="TitleColor">#326cf3</Color>
            <Color x:Key="SecondaryTitleColor">#326cf3</Color>

            <Color x:Key="DefaultColor">White</Color>
            <Color x:Key="DarkDefaultColor">#f5f5f5</Color>

            <Color x:Key="AccentColor">#f8491e</Color>
            <Color x:Key="DarkAccentColor">#f8491e</Color>

            <Color x:Key="DarkMaskColor">#20000000</Color>
            <Color x:Key="DarkOpacityColor">#40000000</Color>
            <system:UInt32 x:Key="BlurGradientValue">2583691263</system:UInt32>
        </ResourceDictionary>
        <ResourceDictionary x:Key="Dark">
            <Color x:Key="LightPrimaryColor">#044289</Color>
            <Color x:Key="PrimaryColor">#326cf3</Color>
            <Color x:Key="DarkPrimaryColor">#326cf3</Color>

            <Color x:Key="LightDangerColor">#450c0f</Color>
            <Color x:Key="DangerColor">#db3340</Color>
            <Color x:Key="DarkDangerColor">#db3340</Color>

            <Color x:Key="LightWarningColor">#4c3a0f</Color>
            <Color x:Key="WarningColor">#e9af20</Color>
            <Color x:Key="DarkWarningColor">#e9af20</Color>

            <Color x:Key="LightInfoColor">#003c44</Color>
            <Color x:Key="InfoColor">#00bcd4</Color>
            <Color x:Key="DarkInfoColor">#00bcd4</Color>

            <Color x:Key="LightSuccessColor">#113a1b</Color>
            <Color x:Key="SuccessColor">#2db84d</Color>
            <Color x:Key="DarkSuccessColor">#2db84d</Color>

            <Color x:Key="PrimaryTextColor">White</Color>
            <Color x:Key="SecondaryTextColor">#757575</Color>
            <Color x:Key="ThirdlyTextColor">#3f3f46</Color>
            <Color x:Key="ReverseTextColor">#212121</Color>
            <Color x:Key="TextIconColor">White</Color>

            <Color x:Key="BorderColor">#3f3f46</Color>
            <Color x:Key="SecondaryBorderColor">#555555</Color>
            <Color x:Key="BackgroundColor">#eeeeee</Color>
            <Color x:Key="RegionColor">#1c1c1c</Color>
            <Color x:Key="SecondaryRegionColor">#2d2d30</Color>
            <Color x:Key="ThirdlyRegionColor">#424242</Color>
            <Color x:Key="TitleColor">#326cf3</Color>
            <Color x:Key="SecondaryTitleColor">#326cf3</Color>

            <Color x:Key="DefaultColor">#686868</Color>
            <Color x:Key="DarkDefaultColor">#686868</Color>

            <Color x:Key="AccentColor">#ff5722</Color>
            <Color x:Key="DarkAccentColor">#d43f3a</Color>

            <Color x:Key="DarkMaskColor">#40000000</Color>
            <Color x:Key="DarkOpacityColor">#40000000</Color>
            <system:UInt32 x:Key="BlurGradientValue">2583691263</system:UInt32>
        </ResourceDictionary>
    </ResourceDictionary.ThemeDictionaries>
</ResourceDictionary>
