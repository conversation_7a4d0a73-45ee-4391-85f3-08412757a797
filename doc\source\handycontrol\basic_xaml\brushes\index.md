---
title: 画刷
---

画刷基于颜色，现有的画刷定义如下：

| 名称 | 用途 |
|-|-|
| PrimaryBrush | 主色调 |
| DarkPrimaryBrush | 主色调（深色） |
| DangerBrush | 错误、危险 |
| DarkDangerBrush | 错误、危险（深色） |
| WarningBrush | 警告 |
| DarkWarningBrush | 警告（深色） |
| InfoBrush | 信息 |
| DarkInfoBrush | 信息（深色） |
| SuccessBrush | 成功 |
| DarkSuccessBrush | 成功（深色） |
| PrimaryTextBrush | 主文本 |
| SecondaryTextBrush | 次级文本 |
| ThirdlyTextBrush | 末级文本 |
| ReverseTextBrush | 反色文本 |
| TextIconBrush | 一般用于深色背景下的文字或图标 |
| BorderBrush | 边框 |
| SecondaryBorderBrush | 次级边框 |
| BackgroundBrush | 主背景色 |
| RegionBrush | 区域块背景 |
| SecondaryRegionBrush | 次级区域块背景 |
| ThirdlyRegionBrush | 末级区域块背景 |
| TitleBrush | 标题背景 |
| DefaultBrush | 默认颜色 |
| DarkDefaultBrush | 次级默认颜色 |
| AccentBrush | 提醒 |
| DarkAccentBrush | 提醒（深色） |
| DarkMaskBrush | 作为遮罩使用 |
| DarkOpacityBrush | 半透明背景 |

{% note info no-icon %}
用例：`Foreground="{DynamicResource PrimaryBrush}"`
{% endnote %}