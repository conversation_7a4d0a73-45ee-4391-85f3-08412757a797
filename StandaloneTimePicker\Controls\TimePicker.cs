using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using StandaloneTimePicker.Commands;
using StandaloneTimePicker.Controls.Clock;
using StandaloneTimePicker.Data;

namespace StandaloneTimePicker.Controls
{
    /// <summary>
    /// A time picker control that allows users to select time by typing in a text box or using a dropdown clock control
    /// </summary>
    [TemplatePart(Name = ElementRoot, Type = typeof(Grid))]
    [TemplatePart(Name = ElementTextBox, Type = typeof(WatermarkTextBox))]
    [TemplatePart(Name = ElementButton, Type = typeof(Button))]
    [TemplatePart(Name = ElementPopup, Type = typeof(Popup))]
    public class TimePicker : Control
    {
        #region Constants

        private const string ElementRoot = "PART_Root";
        private const string ElementTextBox = "PART_TextBox";
        private const string ElementButton = "PART_Button";
        private const string ElementPopup = "PART_Popup";

        #endregion Constants

        #region Data

        private string _defaultText;
        private ButtonBase _dropDownButton;
        private Popup _popup;
        private bool _disablePopupReopen;
        private WatermarkTextBox _textBox;
        private IDictionary<DependencyProperty, bool> _isHandlerSuspended;
        private DateTime? _originalSelectedTime;

        #endregion Data

        #region Public Events

        /// <summary>
        /// Routed event for selected time changes
        /// </summary>
        public static readonly RoutedEvent SelectedTimeChangedEvent =
            EventManager.RegisterRoutedEvent("SelectedTimeChanged", RoutingStrategy.Direct,
                typeof(EventHandler<FunctionEventArgs<DateTime?>>), typeof(TimePicker));

        /// <summary>
        /// Event raised when the selected time changes
        /// </summary>
        public event EventHandler<FunctionEventArgs<DateTime?>> SelectedTimeChanged
        {
            add => AddHandler(SelectedTimeChangedEvent, value);
            remove => RemoveHandler(SelectedTimeChangedEvent, value);
        }

        /// <summary>
        /// Event raised when the clock is closed
        /// </summary>
        public event RoutedEventHandler ClockClosed;

        /// <summary>
        /// Event raised when the clock is opened
        /// </summary>
        public event RoutedEventHandler ClockOpened;

        #endregion Public Events

        static TimePicker()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(TimePicker), new FrameworkPropertyMetadata(typeof(TimePicker)));
            EventManager.RegisterClassHandler(typeof(TimePicker), GotFocusEvent, new RoutedEventHandler(OnGotFocus));
            KeyboardNavigation.TabNavigationProperty.OverrideMetadata(typeof(TimePicker), new FrameworkPropertyMetadata(KeyboardNavigationMode.Once));
            KeyboardNavigation.IsTabStopProperty.OverrideMetadata(typeof(TimePicker), new FrameworkPropertyMetadata(ValueBoxes.FalseBox));
        }

        /// <summary>
        /// Initializes a new instance of the TimePicker class
        /// </summary>
        public TimePicker()
        {
            CommandBindings.Add(new CommandBinding(ControlCommands.Clear, (s, e) =>
            {
                SetCurrentValue(SelectedTimeProperty, null);
                SetCurrentValue(TextProperty, "");
                _textBox.Text = string.Empty;
            }));
        }

        #region Public Properties

        #region TimeFormat

        /// <summary>
        /// Dependency property for TimeFormat
        /// </summary>
        public static readonly DependencyProperty TimeFormatProperty = DependencyProperty.Register(
            nameof(TimeFormat), typeof(string), typeof(TimePicker), new PropertyMetadata("HH:mm"));

        /// <summary>
        /// Gets or sets the format string used to display the selected time
        /// </summary>
        public string TimeFormat
        {
            get => (string)GetValue(TimeFormatProperty);
            set => SetValue(TimeFormatProperty, value);
        }

        #endregion TimeFormat

        #region DisplayTime

        /// <summary>
        /// Gets or sets the time to display
        /// </summary>
        public DateTime DisplayTime
        {
            get => (DateTime)GetValue(DisplayTimeProperty);
            set => SetValue(DisplayTimeProperty, value);
        }

        /// <summary>
        /// Dependency property for DisplayTime
        /// </summary>
        public static readonly DependencyProperty DisplayTimeProperty =
            DependencyProperty.Register(
                nameof(DisplayTime),
                typeof(DateTime),
                typeof(TimePicker),
                new FrameworkPropertyMetadata(default(DateTime), FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, null, CoerceDisplayTime));

        private static object CoerceDisplayTime(DependencyObject d, object value)
        {
            var dp = (TimePicker)d;
            if (dp.Clock != null)
            {
                dp.Clock.DisplayTime = (DateTime)value;
                return dp.Clock.DisplayTime;
            }
            return value;
        }

        #endregion DisplayTime

        #region IsDropDownOpen

        /// <summary>
        /// Gets or sets whether the dropdown clock is open
        /// </summary>
        public bool IsDropDownOpen
        {
            get => (bool)GetValue(IsDropDownOpenProperty);
            set => SetValue(IsDropDownOpenProperty, ValueBoxes.BooleanBox(value));
        }

        /// <summary>
        /// Dependency property for IsDropDownOpen
        /// </summary>
        public static readonly DependencyProperty IsDropDownOpenProperty =
            DependencyProperty.Register(
                nameof(IsDropDownOpen),
                typeof(bool),
                typeof(TimePicker),
                new FrameworkPropertyMetadata(ValueBoxes.FalseBox, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnIsDropDownOpenChanged, OnCoerceIsDropDownOpen));

        private static object OnCoerceIsDropDownOpen(DependencyObject d, object baseValue)
        {
            return d is TimePicker { IsEnabled: false }
                ? false
                : baseValue;
        }

        private static void OnIsDropDownOpenChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var dp = d as TimePicker;

            var newValue = (bool)e.NewValue;
            if (dp?._popup != null && dp._popup.IsOpen != newValue)
            {
                dp._popup.IsOpen = newValue;
                if (newValue)
                {
                    dp._originalSelectedTime = dp.SelectedTime;

                    dp.Dispatcher.BeginInvoke(DispatcherPriority.Input, (Action)delegate
                    {
                        dp.Clock.Focus();
                    });
                }
            }
        }

        #endregion IsDropDownOpen

        #region SelectedTime

        /// <summary>
        /// Gets or sets the currently selected time
        /// </summary>
        public DateTime? SelectedTime
        {
            get => (DateTime?)GetValue(SelectedTimeProperty);
            set => SetValue(SelectedTimeProperty, value);
        }

        /// <summary>
        /// Dependency property for SelectedTime
        /// </summary>
        public static readonly DependencyProperty SelectedTimeProperty =
            DependencyProperty.Register(
                nameof(SelectedTime),
                typeof(DateTime?),
                typeof(TimePicker),
                new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedTimeChanged, CoerceSelectedTime));

        private static void OnSelectedTimeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is not TimePicker dp) return;

            if (dp.SelectedTime.HasValue)
            {
                var time = dp.SelectedTime.Value;
                dp.SetTextInternal(dp.DateTimeToString(time));
            }

            dp.RaiseEvent(new FunctionEventArgs<DateTime?>(SelectedTimeChangedEvent, dp)
            {
                Info = dp.SelectedTime
            });
        }

        private static object CoerceSelectedTime(DependencyObject d, object value)
        {
            var dp = (TimePicker)d;
            if (dp.Clock is null)
            {
                return (DateTime?)value;
            }

            dp.Clock.SelectedTime = (DateTime?)value;
            return dp.Clock.SelectedTime;
        }

        #endregion SelectedTime

        #region Text

        /// <summary>
        /// Gets or sets the text displayed by the TimePicker
        /// </summary>
        public string Text
        {
            get => (string)GetValue(TextProperty);
            set => SetValue(TextProperty, value);
        }

        /// <summary>
        /// Dependency property for Text
        /// </summary>
        public static readonly DependencyProperty TextProperty =
            DependencyProperty.Register(
                nameof(Text),
                typeof(string),
                typeof(TimePicker),
                new FrameworkPropertyMetadata(string.Empty, OnTextChanged));

        private static void OnTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is TimePicker dp && !dp.IsHandlerSuspended(TextProperty))
            {
                if (e.NewValue is string newValue)
                {
                    if (dp._textBox != null)
                    {
                        dp._textBox.Text = newValue;
                    }
                    else
                    {
                        dp._defaultText = newValue;
                    }

                    dp.SetSelectedTime();
                }
                else
                {
                    dp.SetValueNoCallback(SelectedTimeProperty, null);
                }
            }
        }

        /// <summary>
        /// Sets the local Text property without breaking bindings
        /// </summary>
        /// <param name="value">The text value to set</param>
        private void SetTextInternal(string value)
        {
            SetCurrentValue(TextProperty, value);
        }

        #endregion Text

        /// <summary>
        /// Dependency property for SelectionBrush
        /// </summary>
        public static readonly DependencyProperty SelectionBrushProperty =
            TextBoxBase.SelectionBrushProperty.AddOwner(typeof(TimePicker));

        /// <summary>
        /// Gets or sets the brush used for text selection
        /// </summary>
        public Brush SelectionBrush
        {
            get => (Brush)GetValue(SelectionBrushProperty);
            set => SetValue(SelectionBrushProperty, value);
        }

#if !(NET40 || NET45 || NET451 || NET452 || NET46 || NET461 || NET462 || NET47 || NET471 || NET472)

        /// <summary>
        /// Dependency property for SelectionTextBrush
        /// </summary>
        public static readonly DependencyProperty SelectionTextBrushProperty =
            TextBoxBase.SelectionTextBrushProperty.AddOwner(typeof(TimePicker));

        /// <summary>
        /// Gets or sets the brush used for selected text
        /// </summary>
        public Brush SelectionTextBrush
        {
            get => (Brush)GetValue(SelectionTextBrushProperty);
            set => SetValue(SelectionTextBrushProperty, value);
        }

#endif

        /// <summary>
        /// Dependency property for SelectionOpacity
        /// </summary>
        public static readonly DependencyProperty SelectionOpacityProperty =
            TextBoxBase.SelectionOpacityProperty.AddOwner(typeof(TimePicker));

        /// <summary>
        /// Gets or sets the opacity of the text selection
        /// </summary>
        public double SelectionOpacity
        {
            get => (double)GetValue(SelectionOpacityProperty);
            set => SetValue(SelectionOpacityProperty, value);
        }

        /// <summary>
        /// Dependency property for CaretBrush
        /// </summary>
        public static readonly DependencyProperty CaretBrushProperty =
            TextBoxBase.CaretBrushProperty.AddOwner(typeof(TimePicker));

        /// <summary>
        /// Gets or sets the brush used for the text caret
        /// </summary>
        public Brush CaretBrush
        {
            get => (Brush)GetValue(CaretBrushProperty);
            set => SetValue(CaretBrushProperty, value);
        }

        /// <summary>
        /// Dependency property for Clock
        /// </summary>
        public static readonly DependencyProperty ClockProperty = DependencyProperty.Register(
            nameof(Clock), typeof(ClockBase), typeof(TimePicker), new FrameworkPropertyMetadata(default(Clock.Clock), FrameworkPropertyMetadataOptions.NotDataBindable, OnClockChanged));

        private static void OnClockChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var ctl = (TimePicker)d;

            if (e.OldValue is ClockBase oldClock)
            {
                oldClock.SelectedTimeChanged -= ctl.Clock_SelectedTimeChanged;
                oldClock.Confirmed -= ctl.Clock_Confirmed;
                ctl.SetPopupChild(null);
            }

            if (e.NewValue is ClockBase newClock)
            {
                newClock.ShowConfirmButton = true;
                newClock.SelectedTimeChanged += ctl.Clock_SelectedTimeChanged;
                newClock.Confirmed += ctl.Clock_Confirmed;
                ctl.SetPopupChild(newClock);
            }
        }

        /// <summary>
        /// Gets or sets the clock control used in the dropdown
        /// </summary>
        public ClockBase Clock
        {
            get => (ClockBase)GetValue(ClockProperty);
            set => SetValue(ClockProperty, value);
        }

        #endregion Public Properties

        #region Public Methods

        /// <summary>
        /// Applies the control template
        /// </summary>
        public override void OnApplyTemplate()
        {
            if (DesignerProperties.GetIsInDesignMode(this)) return;
            if (_popup != null)
            {
                _popup.PreviewMouseLeftButtonDown -= PopupPreviewMouseLeftButtonDown;
                _popup.Opened -= PopupOpened;
                _popup.Closed -= PopupClosed;
                _popup.Child = null;
            }

            if (_dropDownButton != null)
            {
                _dropDownButton.Click -= DropDownButton_Click;
                _dropDownButton.MouseLeave -= DropDownButton_MouseLeave;
            }

            if (_textBox != null)
            {
                _textBox.KeyDown -= TextBox_KeyDown;
                _textBox.TextChanged -= TextBox_TextChanged;
                _textBox.LostFocus -= TextBox_LostFocus;
            }

            base.OnApplyTemplate();

            _popup = GetTemplateChild(ElementPopup) as Popup;
            _dropDownButton = GetTemplateChild(ElementButton) as Button;
            _textBox = GetTemplateChild(ElementTextBox) as WatermarkTextBox;

            CheckNull();

            _popup.PreviewMouseLeftButtonDown += PopupPreviewMouseLeftButtonDown;
            _popup.Opened += PopupOpened;
            _popup.Closed += PopupClosed;
            _popup.Child = Clock;

            _dropDownButton.Click += DropDownButton_Click;
            _dropDownButton.MouseLeave += DropDownButton_MouseLeave;

            var selectedTime = SelectedTime;

            if (_textBox != null)
            {
                _textBox.SetBinding(SelectionBrushProperty, new Binding(SelectionBrushProperty.Name) { Source = this });
#if !(NET40 || NET45 || NET451 || NET452 || NET46 || NET461 || NET462 || NET47 || NET471 || NET472)
                _textBox.SetBinding(SelectionTextBrushProperty, new Binding(SelectionTextBrushProperty.Name) { Source = this });
#endif
                _textBox.SetBinding(SelectionOpacityProperty, new Binding(SelectionOpacityProperty.Name) { Source = this });
                _textBox.SetBinding(CaretBrushProperty, new Binding(CaretBrushProperty.Name) { Source = this });

                _textBox.KeyDown += TextBox_KeyDown;
                _textBox.TextChanged += TextBox_TextChanged;
                _textBox.LostFocus += TextBox_LostFocus;

                if (selectedTime == null)
                {
                    if (!string.IsNullOrEmpty(_defaultText))
                    {
                        _textBox.Text = _defaultText;
                        SetSelectedTime();
                    }
                }
                else
                {
                    _textBox.Text = DateTimeToString(selectedTime.Value);
                }
            }

            EnsureClock();

            if (selectedTime is not null)
            {
                SetCurrentValue(DisplayTimeProperty, selectedTime);
            }
            // If selectedTime is null, leave DisplayTime as default (empty)
        }

        /// <summary>
        /// Returns a string representation of the selected time
        /// </summary>
        /// <returns>String representation of the selected time</returns>
        public override string ToString() => SelectedTime?.ToString(TimeFormat) ?? string.Empty;

        #endregion Public Methods

        #region Protected Methods

        /// <summary>
        /// Called when the clock is closed
        /// </summary>
        /// <param name="e">Event arguments</param>
        protected virtual void OnClockClosed(RoutedEventArgs e)
        {
            var handler = ClockClosed;
            handler?.Invoke(this, e);
            Clock?.OnClockClosed();
        }

        /// <summary>
        /// Called when the clock is opened
        /// </summary>
        /// <param name="e">Event arguments</param>
        protected virtual void OnClockOpened(RoutedEventArgs e)
        {
            var handler = ClockOpened;
            handler?.Invoke(this, e);
            Clock?.OnClockOpened();
        }

        #endregion Protected Methods

        #region Private Methods

        private void CheckNull()
        {
            if (_dropDownButton == null || _popup == null || _textBox == null)
                throw new Exception();
        }

        private void TextBox_LostFocus(object sender, RoutedEventArgs e) => SetSelectedTime();

        private void SetIsHandlerSuspended(DependencyProperty property, bool value)
        {
            if (value)
            {
                _isHandlerSuspended ??= new Dictionary<DependencyProperty, bool>(2);
                _isHandlerSuspended[property] = true;
            }
            else
            {
                _isHandlerSuspended?.Remove(property);
            }
        }

        private void SetValueNoCallback(DependencyProperty property, object value)
        {
            SetIsHandlerSuspended(property, true);
            try
            {
                SetCurrentValue(property, value);
            }
            finally
            {
                SetIsHandlerSuspended(property, false);
            }
        }

        private void TextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            SetValueNoCallback(TextProperty, _textBox.Text);
        }

        private bool ProcessTimePickerKey(KeyEventArgs e)
        {
            switch (e.Key)
            {
                case Key.System:
                    {
                        switch (e.SystemKey)
                        {
                            case Key.Down:
                                {
                                    if ((Keyboard.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt)
                                    {
                                        TogglePopup();
                                        return true;
                                    }

                                    break;
                                }
                        }

                        break;
                    }

                case Key.Enter:
                    {
                        SetSelectedTime();
                        return true;
                    }
            }

            return false;
        }

        private void TextBox_KeyDown(object sender, KeyEventArgs e)
        {
            e.Handled = ProcessTimePickerKey(e) || e.Handled;
        }

        private void DropDownButton_MouseLeave(object sender, MouseEventArgs e)
        {
            _disablePopupReopen = false;
        }

        private bool IsHandlerSuspended(DependencyProperty property)
        {
            return _isHandlerSuspended != null && _isHandlerSuspended.ContainsKey(property);
        }

        private void PopupPreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Popup { StaysOpen: false })
            {
                if (_dropDownButton?.InputHitTest(e.GetPosition(_dropDownButton)) != null)
                {
                    _disablePopupReopen = true;
                }
            }
        }

        private void Clock_SelectedTimeChanged(object sender, FunctionEventArgs<DateTime?> e) => SelectedTime = e.Info;

        private void Clock_Confirmed() => TogglePopup();

        private void PopupOpened(object sender, EventArgs e)
        {
            SetCurrentValue(IsDropDownOpenProperty, ValueBoxes.TrueBox);
            Clock?.MoveFocus(new TraversalRequest(FocusNavigationDirection.First));
            OnClockOpened(new RoutedEventArgs());
        }

        private void PopupClosed(object sender, EventArgs e)
        {
            SetCurrentValue(IsDropDownOpenProperty, ValueBoxes.FalseBox);
            if (Clock?.IsKeyboardFocusWithin == true)
            {
                MoveFocus(new TraversalRequest(FocusNavigationDirection.First));
            }
            OnClockClosed(new RoutedEventArgs());
        }

        private void DropDownButton_Click(object sender, RoutedEventArgs e) => TogglePopup();

        private void TogglePopup()
        {
            if (IsDropDownOpen)
            {
                SetCurrentValue(IsDropDownOpenProperty, ValueBoxes.FalseBox);
            }
            else
            {
                if (_disablePopupReopen)
                {
                    _disablePopupReopen = false;
                }
                else
                {
                    SetSelectedTime();
                    SetCurrentValue(IsDropDownOpenProperty, ValueBoxes.TrueBox);
                }
            }
        }

        private void SafeSetText(string s)
        {
            if (string.Compare(Text, s, StringComparison.Ordinal) != 0)
            {
                SetCurrentValue(TextProperty, s);
            }
        }

        private DateTime? ParseText(string text)
        {
            try
            {
                return DateTime.Parse(text);
            }
            catch
            {
                // ignored
            }

            return null;
        }

        private DateTime? SetTextBoxValue(string s)
        {
            if (string.IsNullOrEmpty(s))
            {
                SafeSetText(s);
                return SelectedTime;
            }

            var d = ParseText(s);

            if (d != null)
            {
                SafeSetText(DateTimeToString((DateTime)d));
                return d;
            }

            if (SelectedTime != null)
            {
                var newtext = DateTimeToString((DateTime)SelectedTime);
                SafeSetText(newtext);
                return SelectedTime;
            }
            SafeSetText(DateTimeToString(DisplayTime));
            return DisplayTime;
        }

        private void SetSelectedTime()
        {
            if (_textBox != null)
            {
                if (!string.IsNullOrEmpty(_textBox.Text))
                {
                    var s = _textBox.Text;

                    if (SelectedTime != null)
                    {
                        var selectedTime = DateTimeToString(SelectedTime.Value);

                        if (string.Compare(selectedTime, s, StringComparison.Ordinal) == 0)
                        {
                            return;
                        }
                    }

                    var d = SetTextBoxValue(s);
                    if (!SelectedTime.Equals(d))
                    {
                        SetCurrentValue(SelectedTimeProperty, d);
                        SetCurrentValue(DisplayTimeProperty, d);
                    }
                }
                else
                {
                    if (SelectedTime.HasValue)
                    {
                        SetCurrentValue(SelectedTimeProperty, null);
                    }
                }
            }
            else
            {
                var d = SetTextBoxValue(_defaultText);
                if (!SelectedTime.Equals(d))
                {
                    SetCurrentValue(SelectedTimeProperty, d);
                }
            }
        }

        private string DateTimeToString(DateTime d) => d.ToString(TimeFormat);

        private static void OnGotFocus(object sender, RoutedEventArgs e)
        {
            var picker = (TimePicker)sender;
            if (!e.Handled && picker._textBox != null)
            {
                if (Equals(e.OriginalSource, picker))
                {
                    picker._textBox.Focus();
                    e.Handled = true;
                }
                else if (Equals(e.OriginalSource, picker._textBox))
                {
                    picker._textBox.SelectAll();
                    e.Handled = true;
                }
            }
        }

        private void EnsureClock()
        {
            if (Clock is not null)
            {
                return;
            }

            SetCurrentValue(ClockProperty, new Clock.Clock());
            SetPopupChild(Clock);
        }

        private void SetPopupChild(UIElement element)
        {
            if (_popup is not null)
            {
                _popup.Child = Clock;
            }
        }

        #endregion Private Methods
    }
}
