# Standalone TimePicker Control

A fully functional, independent WPF TimePicker control extracted from the HandyControl library. This component provides time selection functionality without requiring the entire HandyControl library as a dependency.

## Features

- **Time Selection**: Users can select time by typing in a text box or using a dropdown clock control
- **Multiple Clock Types**: Supports both analog clock and list-based clock interfaces
- **Smart Watermark**: Intelligent placeholder text that appears/disappears based on focus and content
- **Keyboard Support**: Full keyboard navigation and input support
- **Customizable**: Easily customizable appearance through WPF styles and templates
- **No External Dependencies**: Works with standard WPF without requiring HandyControl library
- **Event Support**: Comprehensive event handling for time selection changes

## Components Included

### Core Controls
- **TimePicker**: Main time picker control with text input and dropdown clock
- **Clock**: Analog clock control for visual time selection
- **ListClock**: List-based clock control for precise time selection
- **ClockRadioButton**: Specialized radio button for clock hour selection

### Supporting Components
- **WatermarkTextBox**: Text box with placeholder text support
- **SimplePanel**: Lightweight panel for layout
- **CirclePanel**: Panel that arranges children in a circular layout
- **FunctionEventArgs**: Event arguments with additional information
- **ValueBoxes**: Boxed value types for improved performance

## Installation

1. **Add Project Reference**: Add the `StandaloneTimePicker.csproj` to your solution and reference it from your WPF application.

2. **Add Namespace**: Add the namespace to your XAML files:
   ```xml
   xmlns:controls="clr-namespace:StandaloneTimePicker.Controls;assembly=StandaloneTimePicker"
   xmlns:clock="clr-namespace:StandaloneTimePicker.Controls.Clock;assembly=StandaloneTimePicker"
   ```

## Basic Usage

### Simple TimePicker
```xml
<controls:TimePicker x:Name="MyTimePicker" Width="200"/>
```

### TimePicker with ListClock
```xml
<controls:TimePicker x:Name="MyTimePicker" Width="200">
    <controls:TimePicker.Clock>
        <clock:ListClock/>
    </controls:TimePicker.Clock>
</controls:TimePicker>
```

### Code-Behind Usage
```csharp
// Set initial time
MyTimePicker.SelectedTime = DateTime.Now;

// Get selected time
DateTime? selectedTime = MyTimePicker.SelectedTime;

// Handle time changes
MyTimePicker.SelectedTimeChanged += (sender, e) => {
    DateTime? newTime = e.Info;
    // Handle time change
};
```

## Properties

| Property | Type | Description |
|----------|------|-------------|
| `SelectedTime` | `DateTime?` | Gets or sets the currently selected time |
| `TimeFormat` | `string` | Gets or sets the format string for displaying time (default: "HH:mm:ss") |
| `DisplayTime` | `DateTime` | Gets or sets the time to display in the clock |
| `IsDropDownOpen` | `bool` | Gets or sets whether the dropdown clock is open |
| `Text` | `string` | Gets or sets the text displayed in the text box |
| `Clock` | `ClockBase` | Gets or sets the clock control used in the dropdown |

## Events

| Event | Description |
|-------|-------------|
| `SelectedTimeChanged` | Raised when the selected time changes |
| `ClockOpened` | Raised when the dropdown clock is opened |
| `ClockClosed` | Raised when the dropdown clock is closed |

## Customization

### Custom Styles
The component uses standard WPF styling. You can override the default styles by creating custom styles targeting the control types:

```xml
<Style TargetType="controls:TimePicker">
    <Setter Property="Background" Value="LightBlue"/>
    <Setter Property="BorderBrush" Value="DarkBlue"/>
    <!-- Add more customizations -->
</Style>
```

### Custom Clock
You can create custom clock implementations by inheriting from `ClockBase`:

```csharp
public class MyCustomClock : ClockBase
{
    // Implement abstract methods and add custom functionality
}
```

## Project Structure

```
StandaloneTimePicker/
├── Commands/
│   └── ControlCommands.cs          # Command definitions
├── Controls/
│   ├── Clock/
│   │   ├── Clock.cs               # Analog clock control
│   │   ├── ClockBase.cs           # Base class for clocks
│   │   ├── ClockRadioButton.cs    # Clock hour selection button
│   │   └── ListClock.cs           # List-based clock control
│   ├── Panels/
│   │   ├── CirclePanel.cs         # Circular layout panel
│   │   └── SimplePanel.cs         # Lightweight panel
│   ├── TimePicker.cs              # Main TimePicker control
│   └── WatermarkTextBox.cs        # TextBox with placeholder
├── Data/
│   ├── FunctionEventArgs.cs       # Event arguments
│   └── ValueBoxes.cs              # Boxed values
├── Helpers/
│   └── ArithmeticHelper.cs        # Math utilities
├── Themes/
│   └── Generic.xaml               # Default styles and templates
└── Properties/
    └── AssemblyInfo.cs            # Assembly information
```

## Building and Testing

1. **Build the Library**:
   ```bash
   dotnet build StandaloneTimePicker/StandaloneTimePicker.csproj
   ```

2. **Run the Test Application**:
   ```bash
   dotnet run --project StandaloneTimePickerTest/StandaloneTimePickerTest.csproj
   ```

## Compatibility

- **.NET Framework**: 4.6.1 and later
- **.NET Core/.NET**: 3.1 and later (with Windows support)
- **WPF**: All versions supporting the target framework

## License

This component is extracted from the HandyControl library and maintains compatibility with its original license terms. Please refer to the original HandyControl project for licensing information.

## Contributing

Contributions are welcome! Please ensure that:
1. Code follows the existing style and patterns
2. All changes are tested with the test application
3. Documentation is updated for any new features

## Acknowledgments

This component is based on the excellent work done by the HandyControl team. The original TimePicker implementation has been adapted to work as a standalone component while preserving its functionality and design.
