// Menu
// --------------------------------------------------
.menu {
  margin-top: 20px;
  padding-left: 0;
  text-align: center;
}

.menu .menu-item {
  display: inline-block;
  margin: 0 10px;
  list-style: none;

  +mobile() {
    margin-top: 10px;
  }

  a, span.exturl {
    show();
    font-size: 13px;
    line-height: inherit;
    border-bottom: 1px solid $menu-link-border;
    transition-property: border-color;
    the-transition();

    &:hover { border-bottom-color: $menu-link-hover-border; }
  }

  .fa { margin-right: 5px; }
}

.use-motion .menu-item { opacity: 0; }
