﻿<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <AssemblyName>HandyControlDemo</AssemblyName>
    <RootNamespace>HandyControlDemo</RootNamespace>
    <TargetFramework>net40</TargetFramework>
    <ApplicationIcon>..\..\Shared\HandyControlDemo_Shared\Resources\Img\icon.ico</ApplicationIcon>
    <NoWarn>0108;MSB3026;MSB3061</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AvalonEdit" Version="6.0.1" />
    <PackageReference Include="Microsoft.NETFramework.ReferenceAssemblies" Version="1.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="MvvmLightLibs" Version="5.4.1.1" />
    <PackageReference Include="Newtonsoft.Json" Version="11.0.2" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\..\Shared\HandyControlDemo_Shared\Data\MessageToken.cs">
      <Link>Data\MessageToken.cs</Link>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>MessageToken.tt</DependentUpon>
    </Compile>
    <Compile Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\LangProvider.cs">
      <Link>Properties\Langs\LangProvider.cs</Link>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LangProvider.tt</DependentUpon>
    </Compile>
    <Compile Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.Designer.cs">
      <Link>Properties\Langs\Lang.Designer.cs</Link>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Lang.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Properties\AssemblyInfo.cs" />
    <Compile Remove="Properties\Langs\Lang.en.Designer.cs" />
    <Compile Remove="Tools\Helper\CommonHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Shared\HandyControlDemo_Code\HandyControlDemo_Code.csproj" />
    <ProjectReference Include="..\HandyControl_Net_40\HandyControl_Net_40.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Data\DemoInfo.json">
      <Link>Data\DemoInfo.json</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\chrome_dragon.png">
      <Link>Resources\Img\chrome_dragon.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Cover.png">
      <Link>Resources\Img\Cover.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\QQ\1.gif">
      <Link>Resources\Img\QQ\1.gif</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\QQ\2.gif">
      <Link>Resources\Img\QQ\2.gif</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\QQ\3.gif">
      <Link>Resources\Img\QQ\3.gif</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\QQ\4.gif">
      <Link>Resources\Img\QQ\4.gif</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\QQ\5.gif">
      <Link>Resources\Img\QQ\5.gif</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\QQ\6.gif">
      <Link>Resources\Img\QQ\6.gif</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\QQ\7.gif">
      <Link>Resources\Img\QQ\7.gif</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\QQ\8.gif">
      <Link>Resources\Img\QQ\8.gif</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\QQ\9.gif">
      <Link>Resources\Img\QQ\9.gif</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\QQ\10.gif">
      <Link>Resources\Img\QQ\10.gif</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\1.jpg">
      <Link>Resources\Img\1.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\2.jpg">
      <Link>Resources\Img\2.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\3.jpg">
      <Link>Resources\Img\3.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\4.jpg">
      <Link>Resources\Img\4.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\5.jpg">
      <Link>Resources\Img\5.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Album\1.jpg">
      <Link>Resources\Img\Album\1.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Album\10.jpg">
      <Link>Resources\Img\Album\10.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Album\2.jpg">
      <Link>Resources\Img\Album\2.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Album\3.jpg">
      <Link>Resources\Img\Album\3.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Album\4.jpg">
      <Link>Resources\Img\Album\4.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Album\5.jpg">
      <Link>Resources\Img\Album\5.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Album\6.jpg">
      <Link>Resources\Img\Album\6.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Album\7.jpg">
      <Link>Resources\Img\Album\7.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Album\8.jpg">
      <Link>Resources\Img\Album\8.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Album\9.jpg">
      <Link>Resources\Img\Album\9.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Avatar\avatar1.png">
      <Link>Resources\Img\Avatar\avatar1.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Avatar\avatar2.png">
      <Link>Resources\Img\Avatar\avatar2.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Avatar\avatar3.png">
      <Link>Resources\Img\Avatar\avatar3.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Avatar\avatar4.png">
      <Link>Resources\Img\Avatar\avatar4.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Avatar\avatar5.png">
      <Link>Resources\Img\Avatar\avatar5.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Avatar\avatar6.png">
      <Link>Resources\Img\Avatar\avatar6.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\b1.jpg">
      <Link>Resources\Img\b1.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\b2.jpg">
      <Link>Resources\Img\b2.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\car_chase.gif">
      <Link>Resources\Img\car_chase.gif</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Chat\chat_back1.jpg">
      <Link>Resources\Img\Chat\chat_back1.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Chat\chat_back2.jpg">
      <Link>Resources\Img\Chat\chat_back2.jpg</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\cn.png">
      <Link>Resources\Img\Flag\cn.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\ca-Es.png">
      <Link>Resources\Img\Flag\ca-Es.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\cs.png">
      <Link>Resources\Img\Flag\cs.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\en.png">
      <Link>Resources\Img\Flag\en.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\es.png">
      <Link>Resources\Img\Flag\es.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\fa.png">
      <Link>Resources\Img\Flag\fa.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\fr.png">
      <Link>Resources\Img\Flag\fr.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\ja.png">
      <Link>Resources\Img\Flag\ja.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\ko-KR.png">
      <Link>Resources\Img\Flag\ko-KR.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\pt-BR.png">
      <Link>Resources\Img\Flag\pt-BR.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\pl.png">
      <Link>Resources\Img\Flag\pl.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\ru.png">
      <Link>Resources\Img\Flag\ru.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Flag\tr.png">
      <Link>Resources\Img\Flag\tr.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Slack.png">
      <Link>Resources\Img\Slack.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\icon-white.ico">
      <Link>Resources\Img\icon-white.ico</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\icon.ico">
      <Link>Resources\Img\icon.ico</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\under_construction.gif">
      <Link>Resources\Img\under_construction.gif</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\DevOps\DevOps-Boards.png">
      <Link>Resources\Img\DevOps\DevOps-Boards.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\DevOps\DevOps-Overview.png">
      <Link>Resources\Img\DevOps\DevOps-Overview.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\DevOps\DevOps-Pipelines.png">
      <Link>Resources\Img\DevOps\DevOps-Pipelines.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\DevOps\DevOps-Repos.png">
      <Link>Resources\Img\DevOps\DevOps-Repos.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\DevOps\DevOps-TestPlans.png">
      <Link>Resources\Img\DevOps\DevOps-TestPlans.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\fabric-icons.ttf">
      <Link>Resources\fabric-icons.ttf</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\xshd\CSharp-Dark.xshd">
      <Link>Resources\xshd\CSharp-Dark.xshd</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\xshd\XML-Dark.xshd">
      <Link>Resources\xshd\XML-Dark.xshd</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Registry.txt">
      <Link>Resources\Registry.txt</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\Dance.png">
      <Link>Resources\Img\Dance.png</Link>
    </Resource>
    <Resource Include="..\..\Shared\HandyControlDemo_Shared\Resources\Img\cloud.png">
      <Link>Resources\Img\cloud.png</Link>
    </Resource>
  </ItemGroup>
  <ItemGroup>
  <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.tr.resx">
      <Link>Properties\Langs\Lang.tr.resx</Link>
    </EmbeddedResource>        
    <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.ca-ES.resx">
      <Link>Properties\Langs\Lang.ca-ES.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.cs.resx">
      <Link>Properties\Langs\Lang.cs.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.en.resx">
      <Link>Properties\Langs\Lang.en.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.es.resx">
      <Link>Properties\Langs\Lang.es.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.fa.resx">
      <Link>Properties\Langs\Lang.fa.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.fr.resx">
      <Link>Properties\Langs\Lang.fr.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.ja.resx">
      <Link>Properties\Langs\Lang.ja.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.ko-KR.resx">
      <Link>Properties\Langs\Lang.ko-KR.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.pt-BR.resx">
      <Link>Properties\Langs\Lang.pt-BR.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.pl.resx">
      <Link>Properties\Langs\Lang.pl.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.ru.resx">
      <Link>Properties\Langs\Lang.ru.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\Lang.resx">
      <Link>Properties\Langs\Lang.resx</Link>
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Lang.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\Shared\HandyControlDemo_Shared\Properties\App.config">
      <Link>Properties\App.config</Link>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="..\..\Shared\HandyControlDemo_Shared\Data\MessageToken.tt">
      <Link>Data\MessageToken.tt</Link>
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>MessageToken.cs</LastGenOutput>
    </Content>
    <Content Include="..\..\Shared\HandyControlDemo_Shared\Properties\Langs\LangProvider.tt">
      <Link>Properties\Langs\LangProvider.tt</Link>
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>LangProvider.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\Shared\HandyControlDemo_Shared\Data\MessageToken.tt" Link="Data\MessageToken.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </None>
  </ItemGroup>
  <Import Project="..\..\Shared\HandyControlDemo_Shared\HandyControlDemo_Shared.projitems" Label="Shared" />
  <PropertyGroup>
    <PostBuildEvent>
      if not exist Langs md Langs
      rd /s /q Langs\cs
      rd /s /q Langs\en
      rd /s /q Langs\es
      rd /s /q Langs\fa
      rd /s /q Langs\fr
      rd /s /q Langs\ja
      rd /s /q Langs\ko-KR
      rd /s /q Langs\pt-BR
      rd /s /q Langs\pl
      rd /s /q Langs\ru
      rd /s /q Langs\tr
      rd /s /q Langs\ca-Es
      move cs Langs
      move en Langs
      move es Langs
      move fa Langs
      move fr Langs
      move ja Langs
      move ko-KR Langs
      move pt-BR Langs
      move pl Langs
      move ru Langs
      move tr Langs
      move ca-Es Langs
    </PostBuildEvent>
    <ApplicationManifest>..\..\Shared\HandyControlDemo_Shared\app.manifest</ApplicationManifest>
  </PropertyGroup>
</Project>
