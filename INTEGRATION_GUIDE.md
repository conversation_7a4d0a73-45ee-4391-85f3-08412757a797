# Integration Guide - Standalone TimePicker

This guide provides step-by-step instructions for integrating the standalone TimePicker component into your WPF application.

## Quick Start

### 1. Add the Component to Your Project

**Option A: Project Reference (Recommended)**
1. Copy the `StandaloneTimePicker` folder to your solution directory
2. Add the project to your solution:
   ```xml
   <ProjectReference Include="..\StandaloneTimePicker\StandaloneTimePicker.csproj" />
   ```

**Option B: Compiled Assembly**
1. Build the StandaloneTimePicker project
2. Copy the resulting `StandaloneTimePicker.dll` to your project
3. Add a reference to the DLL

### 2. Add Namespace Declarations

Add these namespace declarations to your XAML files:

```xml
<Window x:Class="YourApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:StandaloneTimePicker.Controls;assembly=StandaloneTimePicker"
        xmlns:clock="clr-namespace:StandaloneTimePicker.Controls.Clock;assembly=StandaloneTimePicker">
```

### 3. Use the TimePicker

```xml
<controls:TimePicker x:Name="MyTimePicker" 
                     Width="200" 
                     SelectedTime="{Binding CurrentTime}"/>
```

## Advanced Integration

### Custom Styling

Create custom styles in your application resources:

```xml
<Application.Resources>
    <!-- Override default colors -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#FF6B46C1"/>
    <SolidColorBrush x:Key="BorderBrush" Color="#FFD1D5DB"/>
    
    <!-- Custom TimePicker style -->
    <Style x:Key="MyTimePickerStyle" TargetType="controls:TimePicker">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Padding" Value="10,5"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>
</Application.Resources>
```

### Data Binding

```xml
<!-- In your XAML -->
<controls:TimePicker SelectedTime="{Binding SelectedTime, Mode=TwoWay}"
                     TimeFormat="{Binding TimeFormat}"/>
```

```csharp
// In your ViewModel
public class MainViewModel : INotifyPropertyChanged
{
    private DateTime? _selectedTime = DateTime.Now;
    private string _timeFormat = "HH:mm:ss";

    public DateTime? SelectedTime
    {
        get => _selectedTime;
        set
        {
            _selectedTime = value;
            OnPropertyChanged();
        }
    }

    public string TimeFormat
    {
        get => _timeFormat;
        set
        {
            _timeFormat = value;
            OnPropertyChanged();
        }
    }

    // INotifyPropertyChanged implementation...
}
```

### Event Handling

```csharp
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        
        // Subscribe to time change events
        MyTimePicker.SelectedTimeChanged += OnTimeChanged;
        MyTimePicker.ClockOpened += OnClockOpened;
        MyTimePicker.ClockClosed += OnClockClosed;
    }

    private void OnTimeChanged(object sender, FunctionEventArgs<DateTime?> e)
    {
        DateTime? newTime = e.Info;
        MessageBox.Show($"Time changed to: {newTime?.ToString("HH:mm:ss") ?? "None"}");
    }

    private void OnClockOpened(object sender, RoutedEventArgs e)
    {
        Console.WriteLine("Clock opened");
    }

    private void OnClockClosed(object sender, RoutedEventArgs e)
    {
        Console.WriteLine("Clock closed");
    }
}
```

## Common Use Cases

### 1. Time Range Selection

```xml
<StackPanel Orientation="Horizontal">
    <TextBlock Text="From:" VerticalAlignment="Center" Margin="0,0,10,0"/>
    <controls:TimePicker x:Name="StartTime" Width="120"/>
    <TextBlock Text="To:" VerticalAlignment="Center" Margin="20,0,10,0"/>
    <controls:TimePicker x:Name="EndTime" Width="120"/>
</StackPanel>
```

### 2. Different Clock Types

```xml
<!-- Analog Clock (Default) -->
<controls:TimePicker x:Name="AnalogTimePicker"/>

<!-- List Clock -->
<controls:TimePicker x:Name="ListTimePicker">
    <controls:TimePicker.Clock>
        <clock:ListClock/>
    </controls:TimePicker.Clock>
</controls:TimePicker>
```

### 3. Validation

```csharp
private void ValidateTime()
{
    if (MyTimePicker.SelectedTime.HasValue)
    {
        var time = MyTimePicker.SelectedTime.Value;
        if (time.Hour < 9 || time.Hour > 17)
        {
            MessageBox.Show("Please select a time between 9:00 AM and 5:00 PM");
            MyTimePicker.SelectedTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 9, 0, 0);
        }
    }
}
```

## Troubleshooting

### Common Issues

1. **TimePicker not appearing**: Ensure you've added the correct namespace declarations
2. **Styles not applying**: Check that the Generic.xaml is included in the build
3. **Events not firing**: Verify event handler signatures match the expected delegates

### Performance Tips

1. **Reuse Clock instances**: If using multiple TimePickers with the same clock type, consider sharing clock instances
2. **Minimize time format changes**: Avoid frequently changing the TimeFormat property
3. **Use data binding**: Prefer data binding over direct property manipulation for better performance

## Migration from HandyControl

If you're migrating from HandyControl's TimePicker:

1. **Namespace changes**: Update namespace references from `HandyControl.Controls` to `StandaloneTimePicker.Controls`
2. **Removed dependencies**: Remove references to HandyControl-specific attached properties like `InfoElement` and `TitleElement`
3. **Style updates**: Update any custom styles to use the new resource keys

### Before (HandyControl):
```xml
<hc:TimePicker hc:InfoElement.Title="Select Time" 
               hc:InfoElement.ShowClearButton="True"/>
```

### After (Standalone):
```xml
<controls:TimePicker x:Name="TimePicker">
    <!-- Add custom clear button if needed -->
</controls:TimePicker>
```

## Support

For issues and questions:
1. Check the test application for working examples
2. Review the source code for implementation details
3. Create issues in the project repository

## Next Steps

- Explore the test application for comprehensive examples
- Customize the appearance to match your application's theme
- Consider extending the component for specific use cases
