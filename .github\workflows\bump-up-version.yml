name: Bump up version

on:
  workflow_dispatch:
    inputs:
      pre_release:
        description: 'is preview'
        type: boolean
        required: true
        default: false

jobs:
  bump-up-version:
    runs-on: windows-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@master
        with:
          ref: ${{ github.head_ref }}

      - name: Setup .NET Core SDK
        uses: actions/setup-dotnet@master
        with:
          dotnet-version: '8.0.x'
          include-prerelease: true

      - name: Publish
        working-directory: ./build
        run: |
            git config --local user.name "${{ github.actor }}"
            git config --local user.email "<EMAIL>"
            dotnet new tool-manifest
            dotnet tool install cake.tool --version 4.0.0
            dotnet tool install minver-cli --version 3.0.0
            dotnet cake --username="${{ github.actor }}" --email="<EMAIL>" --pre-release=${{ inputs.pre_release }}

      - name: Push git changes
        uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          branch: ${{ github.ref }}
          tags: true

      - name: Push to nuget
        run: dotnet nuget push ./build/outputs/nuget/*nupkg -k ${{ secrets.NUGETTOKEN }}
