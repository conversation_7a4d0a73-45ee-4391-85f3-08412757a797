<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:StandaloneTimePicker.Controls"
                    xmlns:clock="clr-namespace:StandaloneTimePicker.Controls.Clock"
                    xmlns:panels="clr-namespace:StandaloneTimePicker.Controls.Panels">

    <!-- Colors and Brushes -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#007ACC"/>
    <SolidColorBrush x:Key="PrimaryTextBrush" Color="#333333"/>
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="#666666"/>
    <SolidColorBrush x:Key="ThirdlyTextBrush" Color="#999999"/>
    <SolidColorBrush x:Key="TextIconBrush" Color="White"/>
    <SolidColorBrush x:Key="BorderBrush" Color="#CCCCCC"/>
    <SolidColorBrush x:Key="RegionBrush" Color="White"/>
    <SolidColorBrush x:Key="SecondaryRegionBrush" Color="#F5F5F5"/>
    <SolidColorBrush x:Key="TitleBrush" Color="#E8E8E8"/>
    <SolidColorBrush x:Key="DangerBrush" Color="#FF4444"/>

    <!-- Corner Radius -->
    <CornerRadius x:Key="DefaultCornerRadius">4</CornerRadius>

    <!-- Default Control Height -->
    <sys:Double x:Key="DefaultControlHeight" xmlns:sys="clr-namespace:System;assembly=mscorlib">30</sys:Double>

    <!-- Effect Shadow -->
    <DropShadowEffect x:Key="EffectShadow2" BlurRadius="8" ShadowDepth="2" Color="#22000000"/>

    <!-- Boolean to Visibility Converter -->
    <BooleanToVisibilityConverter x:Key="Boolean2VisibilityConverter"/>

    <!-- WatermarkTextBox Style -->
    <Style TargetType="{x:Type controls:WatermarkTextBox}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="CaretBrush" Value="Black"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type controls:WatermarkTextBox}">
                    <Grid>
                        <ScrollViewer x:Name="PART_ContentHost"
                                      Focusable="false"
                                      HorizontalScrollBarVisibility="Hidden"
                                      VerticalScrollBarVisibility="Hidden"/>
                        <TextBlock x:Name="WatermarkText"
                                   Text="{TemplateBinding Watermark}"
                                   Foreground="#999999"
                                   IsHitTestVisible="False"
                                   Margin="{TemplateBinding Padding}"
                                   VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                   HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                   Visibility="Collapsed"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <!-- Show watermark only when text is empty AND control is not focused -->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Text" Value=""/>
                                <Condition Property="IsFocused" Value="False"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="WatermarkText" Property="Visibility" Value="Visible"/>
                        </MultiTrigger>

                        <!-- Show watermark when text is null AND control is not focused -->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Text" Value="{x:Null}"/>
                                <Condition Property="IsFocused" Value="False"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="WatermarkText" Property="Visibility" Value="Visible"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Button Custom Style -->
    <Style x:Key="ButtonCustom" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource DefaultCornerRadius}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"
                                          Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource SecondaryRegionBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource TextIconBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Button Icon Style -->
    <Style x:Key="ButtonIcon" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="4"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="2">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"
                                          Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource SecondaryRegionBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Clock Radio Button Style -->
    <Style x:Key="ClockRadioButtonStyle" TargetType="clock:ClockRadioButton">
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="Width" Value="28"/>
        <Setter Property="Height" Value="28"/>
        <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="clock:ClockRadioButton">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="Storyboard1">
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="Opacity" Storyboard.TargetName="optionMark">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="1"/>
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="Storyboard2">
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="Opacity" Storyboard.TargetName="optionMark">
                                <EasingDoubleKeyFrame KeyTime="0" Value="0"/>
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <panels:SimplePanel x:Name="templateRoot" Background="Transparent" SnapsToDevicePixels="True">
                        <Ellipse x:Name="optionMark" Fill="{TemplateBinding Background}" MinWidth="6" MinHeight="6" Opacity="0"/>
                        <ContentPresenter x:Name="contentPresenter" Focusable="False" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="{TemplateBinding Padding}" RecognizesAccessKey="True" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </panels:SimplePanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="true">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource Storyboard1}"/>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource Storyboard2}"/>
                            </Trigger.ExitActions>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsChecked" Value="True">
                <Setter Property="Foreground" Value="{StaticResource TextIconBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Clock Base Style -->
    <Style x:Key="ClockBaseStyle" TargetType="clock:Clock">
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Margin" Value="8 0"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="Background" Value="{StaticResource RegionBrush}"/>
        <Setter Property="ClockRadioButtonStyle" Value="{StaticResource ClockRadioButtonStyle}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="clock:Clock">
                    <panels:SimplePanel Margin="0,4,0,8" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Border CornerRadius="{StaticResource DefaultCornerRadius}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" Effect="{StaticResource EffectShadow2}"/>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="50"/>
                                <RowDefinition/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition/>
                                <ColumnDefinition/>
                                <ColumnDefinition/>
                            </Grid.ColumnDefinitions>
                            <Border Margin="4" CornerRadius="{StaticResource DefaultCornerRadius}" Grid.ColumnSpan="3" Background="{StaticResource TitleBrush}" Name="PART_BorderTitle">
                                <TextBlock x:Name="PART_TimeStr" VerticalAlignment="Center" HorizontalAlignment="Center" FontSize="20" Foreground="{StaticResource TextIconBrush}"/>
                            </Border>
                            <Canvas Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3" Margin="16,16,16,62" VerticalAlignment="Top" Name="PART_Canvas" Width="178" Height="178">
                                <Border Background="{StaticResource SecondaryRegionBrush}" Width="178" Height="178" CornerRadius="89"/>
                                <panels:CirclePanel Diameter="130" x:Name="PART_PanelNum" KeepVertical="True" Margin="24,24,0,0" OffsetAngle="-60"/>
                                <Border Focusable="False" Name="PART_BorderClock"  Background="{StaticResource PrimaryBrush}" RenderTransformOrigin="0.5,1" Height="63" Width="2" Canvas.Left="88" Canvas.Top="26"/>
                                <Ellipse Fill="White" Width="8" Height="8" StrokeThickness="2" Stroke="{StaticResource PrimaryBrush}" Canvas.Top="85" Canvas.Left="85"/>
                            </Canvas>
                            <clock:ClockRadioButton Grid.Row="1" Background="{StaticResource PrimaryBrush}" Grid.Column="0" x:Name="PART_ButtonAm" IsChecked="True" Height="30" Width="30" HorizontalAlignment="Left" VerticalAlignment="Bottom" Margin="16,0,0,16" Content="AM" Style="{StaticResource ClockRadioButtonStyle}"/>
                            <clock:ClockRadioButton Grid.Row="1" Background="{StaticResource PrimaryBrush}" Grid.Column="2" x:Name="PART_ButtonPm" Height="30" Width="30" HorizontalAlignment="Right" VerticalAlignment="Bottom" Margin="0,0,16,16" Content="PM" Style="{StaticResource ClockRadioButtonStyle}"/>
                            <Button Grid.Row="1" Grid.Column="1" Visibility="{TemplateBinding ShowConfirmButton,Converter={StaticResource Boolean2VisibilityConverter}}" Name="PART_ButtonConfirm" Content="Confirm" Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center" Margin="0,0,0,19" Height="32" Width="80" VerticalAlignment="Bottom" Background="Transparent" Style="{StaticResource ButtonCustom}"/>
                        </Grid>
                    </panels:SimplePanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Default Clock Style -->
    <Style TargetType="clock:Clock" BasedOn="{StaticResource ClockBaseStyle}"/>

    <!-- Clock ListBox Item Style -->
    <Style x:Key="ClockListBoxItemStyle" TargetType="ListBoxItem">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListBoxItem">
                    <Border x:Name="Bd" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" Padding="{TemplateBinding Padding}" SnapsToDevicePixels="true">
                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Background" Value="{StaticResource SecondaryRegionBrush}"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="true">
                            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource TextIconBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Opacity" Value=".4"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="true">
                <Setter Property="Background" Value="{StaticResource SecondaryRegionBrush}"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="true">
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource TextIconBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value=".4"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- HandyControl ScrollBar Styles -->
    <Style x:Key="ScrollBarThumb" TargetType="Thumb">
        <Setter Property="OverridesDefaultStyle" Value="True"/>
        <Setter Property="IsTabStop" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Thumb">
                    <Border x:Name="rectangle"
                            Background="{StaticResource ThirdlyTextBrush}"
                            Height="{TemplateBinding Height}"
                            SnapsToDevicePixels="True"
                            Width="{TemplateBinding Width}"
                            CornerRadius="4"
                            Opacity="0.3"/>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Opacity" TargetName="rectangle" Value="0.6"/>
                        </Trigger>
                        <Trigger Property="IsDragging" Value="True">
                            <Setter Property="Opacity" TargetName="rectangle" Value="0.8"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ScrollBarTrackThumb" TargetType="Thumb">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Thumb">
                    <Grid x:Name="Grid">
                        <Rectangle HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Width="Auto" Height="Auto" Fill="Transparent"/>
                        <Border x:Name="CornerScrollBarRectangle" CornerRadius="4" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Width="Auto" Height="Auto" Margin="0,1,0,1" Background="{TemplateBinding Background}"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Tag" Value="Horizontal">
                            <Setter Property="Width" TargetName="CornerScrollBarRectangle" Value="Auto"/>
                            <Setter Property="Height" TargetName="CornerScrollBarRectangle" Value="6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="ScrollBar">
        <Setter Property="Stylus.IsPressAndHoldEnabled" Value="False"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Width" Value="8"/>
        <Setter Property="MinWidth" Value="8"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollBar">
                    <Grid x:Name="GridRoot" Background="{TemplateBinding Background}">
                        <Track x:Name="PART_Track" IsDirectionReversed="True" Focusable="False">
                            <Track.Thumb>
                                <Thumb x:Name="Thumb" Background="{StaticResource ThirdlyTextBrush}" Style="{StaticResource ScrollBarTrackThumb}"/>
                            </Track.Thumb>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton x:Name="PageUp" Command="ScrollBar.PageDownCommand" Opacity="0" Focusable="False"/>
                            </Track.IncreaseRepeatButton>
                            <Track.DecreaseRepeatButton>
                                <RepeatButton x:Name="PageDown" Command="ScrollBar.PageUpCommand" Opacity="0" Focusable="False"/>
                            </Track.DecreaseRepeatButton>
                        </Track>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger SourceName="Thumb" Property="IsMouseOver" Value="True">
                            <Setter Value="{StaticResource SecondaryTextBrush}" TargetName="Thumb" Property="Background"/>
                        </Trigger>
                        <Trigger SourceName="Thumb" Property="IsDragging" Value="True">
                            <Setter Value="{StaticResource PrimaryTextBrush}" TargetName="Thumb" Property="Background"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Thumb" Property="Visibility" Value="Collapsed"/>
                        </Trigger>
                        <Trigger Property="Orientation" Value="Horizontal">
                            <Setter Property="Width" Value="Auto"/>
                            <Setter Property="MinWidth" Value="0"/>
                            <Setter Property="Height" Value="8"/>
                            <Setter Property="MinHeight" Value="8"/>
                            <Setter Property="BorderThickness" Value="0,1"/>
                            <Setter TargetName="Thumb" Property="Tag" Value="Horizontal"/>
                            <Setter TargetName="PageDown" Property="Command" Value="ScrollBar.PageLeftCommand"/>
                            <Setter TargetName="PageUp" Property="Command" Value="ScrollBar.PageRightCommand"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Orientation" Value="Horizontal">
                <Setter Property="Width" Value="Auto"/>
                <Setter Property="MinWidth" Value="0"/>
                <Setter Property="Height" Value="8"/>
                <Setter Property="MinHeight" Value="8"/>
                <Setter Property="Background" Value="Transparent"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Custom ScrollViewer for ListBox with thin scrollbars -->
    <Style x:Key="ThinScrollViewer" TargetType="ScrollViewer">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollViewer">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="8"/>
                        </Grid.ColumnDefinitions>

                        <ScrollContentPresenter Grid.Column="0"
                                                Content="{TemplateBinding Content}"
                                                ContentTemplate="{TemplateBinding ContentTemplate}"
                                                CanContentScroll="{TemplateBinding CanContentScroll}"/>

                        <ScrollBar Grid.Column="1"
                                   x:Name="PART_VerticalScrollBar"
                                   Orientation="Vertical"
                                   Value="{TemplateBinding VerticalOffset}"
                                   Maximum="{TemplateBinding ScrollableHeight}"
                                   ViewportSize="{TemplateBinding ViewportHeight}"
                                   Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}">
                            <ScrollBar.Template>
                                <ControlTemplate TargetType="ScrollBar">
                                    <Border Background="Transparent" Width="8">
                                        <Track x:Name="PART_Track" IsDirectionReversed="True" Focusable="False">
                                            <Track.Thumb>
                                                <Thumb>
                                                    <Thumb.Template>
                                                        <ControlTemplate TargetType="Thumb">
                                                            <Border Background="{StaticResource ThirdlyTextBrush}"
                                                                    CornerRadius="4"
                                                                    Width="6"
                                                                    Margin="1,0"/>
                                                            <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="{StaticResource SecondaryTextBrush}"/>
                                                                </Trigger>
                                                                <Trigger Property="IsDragging" Value="True">
                                                                    <Setter Property="Background" Value="{StaticResource PrimaryTextBrush}"/>
                                                                </Trigger>
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </Thumb.Template>
                                                </Thumb>
                                            </Track.Thumb>
                                            <Track.IncreaseRepeatButton>
                                                <RepeatButton Command="ScrollBar.PageDownCommand" Opacity="0" Focusable="False"/>
                                            </Track.IncreaseRepeatButton>
                                            <Track.DecreaseRepeatButton>
                                                <RepeatButton Command="ScrollBar.PageUpCommand" Opacity="0" Focusable="False"/>
                                            </Track.DecreaseRepeatButton>
                                        </Track>
                                    </Border>
                                </ControlTemplate>
                            </ScrollBar.Template>
                        </ScrollBar>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Clock ListBox Style -->
    <Style x:Key="ClockListBoxStyle" TargetType="ListBox">
        <Setter Property="MaxHeight" Value="180"/>
        <Setter Property="ScrollViewer.CanContentScroll" Value="True"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="Background" Value="{StaticResource RegionBrush}"/>
        <Setter Property="ItemContainerStyle" Value="{StaticResource ClockListBoxItemStyle}"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListBox">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <ScrollViewer Style="{StaticResource ThinScrollViewer}"
                                      Focusable="False"
                                      CanContentScroll="{TemplateBinding ScrollViewer.CanContentScroll}"
                                      HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                      VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}">
                            <ItemsPresenter/>
                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ListClock Base Style -->
    <Style x:Key="ListClockBaseStyle" TargetType="clock:ListClock">
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Margin" Value="8 0"/>
        <Setter Property="Focusable" Value="False"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="Background" Value="{StaticResource RegionBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="clock:ListClock">
                    <panels:SimplePanel Margin="0,4,0,8" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Border CornerRadius="{StaticResource DefaultCornerRadius}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" Effect="{StaticResource EffectShadow2}"/>
                        <Grid Margin="0,4" Width="140">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition/>
                                <ColumnDefinition/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <ListBox Padding="0" x:Name="PART_HourList" Style="{StaticResource ClockListBoxStyle}"/>
                            <ListBox Padding="0" Grid.Row="0" Grid.Column="1" x:Name="PART_MinuteList" Style="{StaticResource ClockListBoxStyle}" BorderThickness="1,0,0,0"/>
                            <Border VerticalAlignment="Bottom" Visibility="{Binding Visibility,ElementName=PART_ButtonConfirm}" Height="1" Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" Background="{StaticResource BorderBrush}"/>
                            <Button Margin="0,10" Grid.Column="0" Grid.ColumnSpan="2" Grid.Row="1" Visibility="{TemplateBinding ShowConfirmButton,Converter={StaticResource Boolean2VisibilityConverter}}" Name="PART_ButtonConfirm" Content="Confirm" Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center" Background="Transparent" Style="{StaticResource ButtonCustom}"/>
                        </Grid>
                    </panels:SimplePanel>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Default ListClock Style -->
    <Style TargetType="clock:ListClock" BasedOn="{StaticResource ListClockBaseStyle}"/>

    <!-- TimePicker Base Style -->
    <Style x:Key="TimePickerBaseStyle" TargetType="controls:TimePicker">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="CaretBrush" Value="Black"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="controls:TimePicker">
                    <Border x:Name="MainBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <Grid x:Name="PART_Root">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="32"/>
                            </Grid.ColumnDefinitions>

                            <!-- Text Input Area -->
                            <controls:WatermarkTextBox x:Name="PART_TextBox"
                                                       Grid.Column="0"
                                                       Background="Transparent"
                                                       BorderThickness="0"
                                                       Padding="{TemplateBinding Padding}"
                                                       VerticalAlignment="Center"
                                                       Foreground="{TemplateBinding Foreground}"
                                                       CaretBrush="{TemplateBinding CaretBrush}"
                                                       FontSize="{TemplateBinding FontSize}"
                                                       Watermark="Select time..."/>

                            <!-- Dropdown Button -->
                            <Button x:Name="PART_Button"
                                    Grid.Column="1"
                                    Background="#F5F5F5"
                                    BorderBrush="#CCCCCC"
                                    BorderThickness="1,0,0,0"
                                    Width="32"
                                    VerticalAlignment="Stretch"
                                    HorizontalAlignment="Stretch"
                                    Cursor="Hand">
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border x:Name="ButtonBorder"
                                                Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                              VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter TargetName="ButtonBorder" Property="Background" Value="#E5E5E5"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter TargetName="ButtonBorder" Property="Background" Value="#D0D0D0"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Button.Template>
                                <Path Data="M 0 0 L 6 0 L 3 4 Z"
                                      Fill="#666666"
                                      Width="8"
                                      Height="5"
                                      Stretch="Uniform"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                            </Button>

                            <!-- Popup -->
                            <Popup x:Name="PART_Popup"
                                   AllowsTransparency="True"
                                   Placement="Bottom"
                                   PlacementTarget="{Binding ElementName=PART_Root}"
                                   StaysOpen="False"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="MainBorder" Property="BorderBrush" Value="#007ACC"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="MainBorder" Property="BorderBrush" Value="#007ACC"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Default TimePicker Style -->
    <Style TargetType="controls:TimePicker" BasedOn="{StaticResource TimePickerBaseStyle}"/>

</ResourceDictionary>
