{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\HandyControl-master\\src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontrol_shared\\themes\\styles\\timepicker.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|solutionrelative:shared\\handycontrol_shared\\themes\\styles\\timepicker.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontrol_shared\\themes\\theme.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|solutionrelative:shared\\handycontrol_shared\\themes\\theme.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{D8A4748C-0500-4F63-BC47-3506658C68F7}|Shared\\HandyControlDemo_Code\\HandyControlDemo_Code.csproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontroldemo_shared\\usercontrol\\controls\\numericupdowndemoctl.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{D8A4748C-0500-4F63-BC47-3506658C68F7}|Shared\\HandyControlDemo_Code\\HandyControlDemo_Code.csproj|solutionrelative:shared\\handycontroldemo_shared\\usercontrol\\controls\\numericupdowndemoctl.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\3d515aa49b359827bb3b97dd45c1839f353811f6e1a845162d6bcc9f7aa9f78f\\HwndMouseInputProvider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C4694269-C9B8-45D5-87F8-D0088C532510}|Shared\\HandyControlDemo_Shared\\HandyControlDemo_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontroldemo_shared\\resources\\themes\\theme.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{C4694269-C9B8-45D5-87F8-D0088C532510}|Shared\\HandyControlDemo_Shared\\HandyControlDemo_Shared.shproj|solutionrelative:shared\\handycontroldemo_shared\\resources\\themes\\theme.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontrol_shared\\themes\\styles\\button.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|solutionrelative:shared\\handycontrol_shared\\themes\\styles\\button.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontrol_shared\\controls\\attach\\iconswitchelement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|solutionrelative:shared\\handycontrol_shared\\controls\\attach\\iconswitchelement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontrol_shared\\controls\\attach\\borderelement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|solutionrelative:shared\\handycontrol_shared\\controls\\attach\\borderelement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontrol_shared\\controls\\panel\\simplepanel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|solutionrelative:shared\\handycontrol_shared\\controls\\panel\\simplepanel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontrol_shared\\themes\\styles\\splitbutton.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|solutionrelative:shared\\handycontrol_shared\\themes\\styles\\splitbutton.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontrol_shared\\controls\\attach\\iconelement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|solutionrelative:shared\\handycontrol_shared\\controls\\attach\\iconelement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontrol_shared\\controls\\button\\splitbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|solutionrelative:shared\\handycontrol_shared\\controls\\button\\splitbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontrol_shared\\themes\\styles\\base\\buttonbasebasestyle.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{AAC11083-FACA-405D-9197-5C1212D65656}|Shared\\HandyControl_Shared\\HandyControl_Shared.shproj|solutionrelative:shared\\handycontrol_shared\\themes\\styles\\base\\buttonbasebasestyle.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{C4694269-C9B8-45D5-87F8-D0088C532510}|Shared\\HandyControlDemo_Shared\\HandyControlDemo_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontroldemo_shared\\viewmodel\\controls\\splitbuttondemoviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C4694269-C9B8-45D5-87F8-D0088C532510}|Shared\\HandyControlDemo_Shared\\HandyControlDemo_Shared.shproj|solutionrelative:shared\\handycontroldemo_shared\\viewmodel\\controls\\splitbuttondemoviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{34D39209-C744-41AA-91D6-C15C68E2A1D9}|Avalonia\\HandyControl_Avalonia\\HandyControl_Avalonia.csproj|d:\\projects\\handycontrol-master\\src\\avalonia\\handycontrol_avalonia\\themes\\styles\\usercontrol.axaml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{34D39209-C744-41AA-91D6-C15C68E2A1D9}|Avalonia\\HandyControl_Avalonia\\HandyControl_Avalonia.csproj|solutionrelative:avalonia\\handycontrol_avalonia\\themes\\styles\\usercontrol.axaml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{6F0C9CFF-2269-46A7-9664-478354C582A4}|Shared\\System.Windows.Interactivity\\System.Windows.Interactivity.shproj|d:\\projects\\handycontrol-master\\src\\shared\\system.windows.interactivity\\behavior.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6F0C9CFF-2269-46A7-9664-478354C582A4}|Shared\\System.Windows.Interactivity\\System.Windows.Interactivity.shproj|solutionrelative:shared\\system.windows.interactivity\\behavior.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C4694269-C9B8-45D5-87F8-D0088C532510}|Shared\\HandyControlDemo_Shared\\HandyControlDemo_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontroldemo_shared\\viewmodel\\basic\\chatboxviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C4694269-C9B8-45D5-87F8-D0088C532510}|Shared\\HandyControlDemo_Shared\\HandyControlDemo_Shared.shproj|solutionrelative:shared\\handycontroldemo_shared\\viewmodel\\basic\\chatboxviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C4694269-C9B8-45D5-87F8-D0088C532510}|Shared\\HandyControlDemo_Shared\\HandyControlDemo_Shared.shproj|d:\\projects\\handycontrol-master\\src\\shared\\handycontroldemo_shared\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{C4694269-C9B8-45D5-87F8-D0088C532510}|Shared\\HandyControlDemo_Shared\\HandyControlDemo_Shared.shproj|solutionrelative:shared\\handycontroldemo_shared\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 6, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:130:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "TimePicker.xaml", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Themes\\Styles\\TimePicker.xaml", "RelativeDocumentMoniker": "Shared\\HandyControl_Shared\\Themes\\Styles\\TimePicker.xaml", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Themes\\Styles\\TimePicker.xaml", "RelativeToolTip": "Shared\\HandyControl_Shared\\Themes\\Styles\\TimePicker.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-05T03:14:09.319Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "NumericUpDownDemoCtl.xaml", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControlDemo_Shared\\UserControl\\Controls\\NumericUpDownDemoCtl.xaml", "RelativeDocumentMoniker": "Shared\\HandyControlDemo_Shared\\UserControl\\Controls\\NumericUpDownDemoCtl.xaml", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControlDemo_Shared\\UserControl\\Controls\\NumericUpDownDemoCtl.xaml", "RelativeToolTip": "Shared\\HandyControlDemo_Shared\\UserControl\\Controls\\NumericUpDownDemoCtl.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2024-07-22T16:53:29.884Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "HwndMouseInputProvider.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\3d515aa49b359827bb3b97dd45c1839f353811f6e1a845162d6bcc9f7aa9f78f\\HwndMouseInputProvider.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\3d515aa49b359827bb3b97dd45c1839f353811f6e1a845162d6bcc9f7aa9f78f\\HwndMouseInputProvider.cs", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-08T08:18:45.243Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "Theme.xaml", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Themes\\Theme.xaml", "RelativeDocumentMoniker": "Shared\\HandyControl_Shared\\Themes\\Theme.xaml", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Themes\\Theme.xaml", "RelativeToolTip": "Shared\\HandyControl_Shared\\Themes\\Theme.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2024-03-31T05:52:57.482Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "BorderElement.cs", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Controls\\Attach\\BorderElement.cs", "RelativeDocumentMoniker": "Shared\\HandyControl_Shared\\Controls\\Attach\\BorderElement.cs", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Controls\\Attach\\BorderElement.cs", "RelativeToolTip": "Shared\\HandyControl_Shared\\Controls\\Attach\\BorderElement.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAADAAAAABAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-03-31T05:34:55.086Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "SimplePanel.cs", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Controls\\Panel\\SimplePanel.cs", "RelativeDocumentMoniker": "Shared\\HandyControl_Shared\\Controls\\Panel\\SimplePanel.cs", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Controls\\Panel\\SimplePanel.cs", "RelativeToolTip": "Shared\\HandyControl_Shared\\Controls\\Panel\\SimplePanel.cs", "ViewState": "AQIAAAEAAAAAAAAAAAAAACgAAAABAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-03-31T05:23:13.062Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "IconElement.cs", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Controls\\Attach\\IconElement.cs", "RelativeDocumentMoniker": "Shared\\HandyControl_Shared\\Controls\\Attach\\IconElement.cs", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Controls\\Attach\\IconElement.cs", "RelativeToolTip": "Shared\\HandyControl_Shared\\Controls\\Attach\\IconElement.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAACEAAAABAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-03-31T04:48:39.282Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "SplitButton.cs", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Controls\\Button\\SplitButton.cs", "RelativeDocumentMoniker": "Shared\\HandyControl_Shared\\Controls\\Button\\SplitButton.cs", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Controls\\Button\\SplitButton.cs", "RelativeToolTip": "Shared\\HandyControl_Shared\\Controls\\Button\\SplitButton.cs", "ViewState": "AQIAABwAAAAAAAAAAAAAAB4AAABdAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-03-31T01:41:56.962Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "Theme.xaml", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControlDemo_Shared\\Resources\\Themes\\Theme.xaml", "RelativeDocumentMoniker": "Shared\\HandyControlDemo_Shared\\Resources\\Themes\\Theme.xaml", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControlDemo_Shared\\Resources\\Themes\\Theme.xaml", "RelativeToolTip": "Shared\\HandyControlDemo_Shared\\Resources\\Themes\\Theme.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2024-03-31T00:50:52.219Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "Button.xaml", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Themes\\Styles\\Button.xaml", "RelativeDocumentMoniker": "Shared\\HandyControl_Shared\\Themes\\Styles\\Button.xaml", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Themes\\Styles\\Button.xaml", "RelativeToolTip": "Shared\\HandyControl_Shared\\Themes\\Styles\\Button.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2024-03-31T00:36:46.141Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "IconSwitchElement.cs", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Controls\\Attach\\IconSwitchElement.cs", "RelativeDocumentMoniker": "Shared\\HandyControl_Shared\\Controls\\Attach\\IconSwitchElement.cs", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Controls\\Attach\\IconSwitchElement.cs", "RelativeToolTip": "Shared\\HandyControl_Shared\\Controls\\Attach\\IconSwitchElement.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAABMAAAABAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-03-31T05:38:06.895Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "SplitButtonDemoViewModel.cs", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControlDemo_Shared\\ViewModel\\Controls\\SplitButtonDemoViewModel.cs", "RelativeDocumentMoniker": "Shared\\HandyControlDemo_Shared\\ViewModel\\Controls\\SplitButtonDemoViewModel.cs", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControlDemo_Shared\\ViewModel\\Controls\\SplitButtonDemoViewModel.cs", "RelativeToolTip": "Shared\\HandyControlDemo_Shared\\ViewModel\\Controls\\SplitButtonDemoViewModel.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-03-31T00:32:02.659Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "SplitButton.xaml", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Themes\\Styles\\SplitButton.xaml", "RelativeDocumentMoniker": "Shared\\HandyControl_Shared\\Themes\\Styles\\SplitButton.xaml", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Themes\\Styles\\SplitButton.xaml", "RelativeToolTip": "Shared\\HandyControl_Shared\\Themes\\Styles\\SplitButton.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2024-03-31T00:32:50.5Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "ButtonBaseBaseStyle.xaml", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Themes\\Styles\\Base\\ButtonBaseBaseStyle.xaml", "RelativeDocumentMoniker": "Shared\\HandyControl_Shared\\Themes\\Styles\\Base\\ButtonBaseBaseStyle.xaml", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControl_Shared\\Themes\\Styles\\Base\\ButtonBaseBaseStyle.xaml", "RelativeToolTip": "Shared\\HandyControl_Shared\\Themes\\Styles\\Base\\ButtonBaseBaseStyle.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2024-03-31T00:32:26.785Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "UserControl.axaml", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Avalonia\\HandyControl_Avalonia\\Themes\\Styles\\UserControl.axaml", "RelativeDocumentMoniker": "Avalonia\\HandyControl_Avalonia\\Themes\\Styles\\UserControl.axaml", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Avalonia\\HandyControl_Avalonia\\Themes\\Styles\\UserControl.axaml", "RelativeToolTip": "Avalonia\\HandyControl_Avalonia\\Themes\\Styles\\UserControl.axaml", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2024-03-31T00:30:57.778Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "Behavior.cs", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\System.Windows.Interactivity\\Behavior.cs", "RelativeDocumentMoniker": "Shared\\System.Windows.Interactivity\\Behavior.cs", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\System.Windows.Interactivity\\Behavior.cs", "RelativeToolTip": "Shared\\System.Windows.Interactivity\\Behavior.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-03-30T17:42:47.471Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "ChatBoxViewModel.cs", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControlDemo_Shared\\ViewModel\\Basic\\ChatBoxViewModel.cs", "RelativeDocumentMoniker": "Shared\\HandyControlDemo_Shared\\ViewModel\\Basic\\ChatBoxViewModel.cs", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControlDemo_Shared\\ViewModel\\Basic\\ChatBoxViewModel.cs", "RelativeToolTip": "Shared\\HandyControlDemo_Shared\\ViewModel\\Basic\\ChatBoxViewModel.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAHQAAAAPAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-03-30T17:31:23.213Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControlDemo_Shared\\MainWindow.xaml", "RelativeDocumentMoniker": "Shared\\HandyControlDemo_Shared\\MainWindow.xaml", "ToolTip": "D:\\Projects\\HandyControl-master\\src\\Shared\\HandyControlDemo_Shared\\MainWindow.xaml", "RelativeToolTip": "Shared\\HandyControlDemo_Shared\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2024-03-30T17:29:23.203Z"}]}]}]}