body {
  overscroll-behavior: none;
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  body:before {
    right: 0;
    top: 0;
    left: 0;
    height: 100px;
    z-index: 2147483647;
    position: fixed;
    content: "";
    show();
    transform: translateY(-99.99px);
    background: linear-gradient(124deg,
      #FF0000,
      #FF7F00,
      #FFFF00,
      #7FFF00,
      #00FF00,
      #00FF7F,
      #00FFFF,
      #007FFF,
      #0000FF,
      #7F00FF,
      #FF00FF,
      #FF007F,
      #FF0000);
    animation: rainbow 15s ease infinite;
    background-size: 1000% 1000%;
  }
}
@keyframes rainbow {
  0% {
    background-position: 0% 80%;
  }
  50% {
    background-position: 100% 20%;
  }
  100% {
    background-position: 0% 80%;
  }
}
