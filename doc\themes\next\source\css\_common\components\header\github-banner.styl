@keyframes octocat-wave {
  0%, 100% {
    transform: rotate(0);
  }
  20%, 60% {
    transform: rotate(-25deg);
  }
  40%, 80% {
    transform: rotate(10deg);
  }
}

.github-corner {
  scheme = hexo-config('scheme');
  bg_color = unquote(hexo-config('android_chrome_color'));
  mobile_layout_economy = hexo-config('mobile_layout_economy');

  :hover .octo-arm {
    animation: octocat-wave 560ms ease-in-out;
  }
  > svg {
    fill: bg_color;
    color: #fff;
    position: absolute;
    top: 0;
    border: 0;
    right: 0;
  }
  +tablet-mobile() {
    > svg {
      if (scheme == 'Pisces') || (scheme == 'Gemini') {
        fill: #fff;
        color: bg_color;
      }
    }
    .github-corner:hover .octo-arm {
      animation: none;
    }
    .github-corner .octo-arm {
      animation: octocat-wave 560ms ease-in-out;
    }
  }
  +mobile() {
    > svg {
      if (scheme == 'Mist') {
        top: inherit;
        if mobile_layout_economy {
          +mobile-small() {
            margin-top: initial;
          }
        }
      }
    }
  }
}
