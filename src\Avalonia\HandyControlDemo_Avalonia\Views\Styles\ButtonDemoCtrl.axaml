﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignWidth="800"
             d:DesignHeight="450"
             x:Class="HandyControlDemo.Views.ButtonDemoCtrl">
	<StackPanel Orientation="Horizontal" VerticalAlignment="Top" Spacing="4" Margin="32">
        <Button Content="default" />
        <Button Content="primary" Classes="primary"/>
        <Button Content="success" Classes="success"/>
        <Button Content="info" Classes="info"/>
        <Button Content="warning" Classes="warning"/>
        <Button Content="danger" Classes="danger"/>
    </StackPanel>
</UserControl>
