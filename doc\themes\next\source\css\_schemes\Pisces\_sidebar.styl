.use-motion .sidebar .motion-element { opacity: 1; }

.sidebar {
  right: auto;
  bottom: auto;

  // Do NOT delete this line
  // or Affix (position: fixed) will not work in Google Chrome.
  -webkit-transform: none;

  a, span.exturl {
    color: $black-light;

    &:hover {
      color: $black-deep;
      border-bottom-color: $black-deep;
    }
  }
}

.sidebar-inner {
  //padding: 20px 10px 0;
  box-sizing: border-box;
  width: $sidebar-desktop;
  color: $text-color;
  background: white;
  box-shadow: $box-shadow;
  border-radius: $border-radius;
  if (hexo-config('motion.enable') and hexo-config('motion.transition.sidebar')) { opacity: 0; }

  &.affix {
    position: fixed;
    top: $sidebar-offset;
  }

  &.affix-bottom {
    position: absolute;
  }
}

.site-overview {
  //margin: 0 2px;
  text-align: left;
}

.site-author {
  clearfix();
}

.site-state-item {
  padding: 0 10px;
}

.feed-link, .chat {
  border-top: 1px dotted $grey-light;
  border-bottom: 1px dotted $grey-light;
  text-align: center;
  a {
    show();
    color: $orange;
    border: none !important;

    &:hover {
      background: none;
      color: darken($orange, 20%);

      i { color: darken($orange, 20%); }
    }
  }
}

.links-of-author {
  //clearfix();
  display: flex;
  flex-wrap: wrap;
  justify-content: center;

  span.exturl {
    font-size: 13px;
  }
}

.links-of-author-item {
  sidebar-inline-links-item();
  a:before, span.exturl:before { display: none; }
  a, span.exturl {
    border-bottom: none;
    text-decoration: underline;
  }

  a, span.exturl {
    show();
    text-decoration: none;

    &:hover {
      border-radius: 4px;
      background: $gainsboro;
    }
  }

  .fa {
    margin-right: 2px;
    font-size: 16px;
  }
  .fa-globe { font-size: 15px; }
}

.links-of-blogroll {
  text-align: center;
  padding: 3px 0 0;
}
.links-of-blogroll-item { padding: 0; }
.links-of-blogroll-inline {
  clearfix();

  .links-of-blogroll-item {
    sidebar-inline-links-item();
    display: inline-block;
    if !hexo-config('social_icons.icons_only') { width: unset; }
  }
}
