<Window x:Class="ScrollbarTest.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:StandaloneTimePicker.Controls;assembly=StandaloneTimePicker"
        xmlns:clock="clr-namespace:StandaloneTimePicker.Controls.Clock;assembly=StandaloneTimePicker"
        Title="Thin Scrollbar Test - ListClock" Height="550" Width="750">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0" Text="✅ IMPROVED: Thin Scrollbars in ListClock" 
                   FontSize="20" FontWeight="Bold" Foreground="Green" Margin="0,0,0,20"/>
        
        <TextBlock Grid.Row="1" Text="TimePicker with ListClock (check scrollbar width):" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="2" x:Name="ListClockTimePicker" Width="300" HorizontalAlignment="Left" Margin="0,0,0,15">
            <controls:TimePicker.Clock>
                <clock:ListClock/>
            </controls:TimePicker.Clock>
        </controls:TimePicker>
        
        <TextBlock Grid.Row="3" Text="Standard TimePicker with Analog Clock (for comparison):" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="4" x:Name="AnalogTimePicker" Width="300" HorizontalAlignment="Left"/>
        
        <Border Grid.Row="5" BorderBrush="LightGray" BorderThickness="1" Margin="0,20,0,0" Padding="15">
            <StackPanel>
                <TextBlock Text="✅ THIN SCROLLBAR IMPROVEMENTS:" FontWeight="Bold" FontSize="16" Foreground="DarkBlue" Margin="0,0,0,10"/>
                
                <TextBlock Text="Problem Fixed:" FontWeight="Bold" Margin="0,0,0,5"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Default Windows scrollbars were too wide (16-20px)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Scrollbars dominated visual space in compact ListClock"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Didn't match modern UI design standards"/>
                </StackPanel>
                
                <TextBlock Text="Improvements Made:" FontWeight="Bold" Margin="0,15,0,5"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Created custom ThinScrollBarThumb style with rounded corners"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Reduced scrollbar width from ~16px to 10px (37% thinner)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Added hover and drag states for better user feedback"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Applied custom ScrollViewer template to ClockListBoxStyle"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Maintained full scrolling functionality"/>
                </StackPanel>
                
                <TextBlock Text="Visual Design:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Scrollbar width: 10px (was ~16px)" Margin="0,2"/>
                <TextBlock Text="• Thumb color: Light gray (#CCCCCC) with rounded corners" Margin="0,2"/>
                <TextBlock Text="• Hover state: Medium gray (#999999)" Margin="0,2"/>
                <TextBlock Text="• Drag state: Dark gray (#666666)" Margin="0,2"/>
                <TextBlock Text="• Track: Transparent background for clean appearance" Margin="0,2"/>
                
                <TextBlock Text="Test Instructions:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Click the ListClock TimePicker dropdown button" Margin="0,2"/>
                <TextBlock Text="• Observe the thin scrollbars in the hours and minutes columns" Margin="0,2"/>
                <TextBlock Text="• Scroll through the lists to test functionality" Margin="0,2"/>
                <TextBlock Text="• Hover over scrollbars to see color changes" Margin="0,2"/>
                <TextBlock Text="• Compare with standard Windows controls for size difference" Margin="0,2"/>
                
                <TextBlock Text="Benefits Achieved:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• More screen space for content (37% thinner scrollbars)" Margin="0,2"/>
                <TextBlock Text="• Modern, clean visual appearance" Margin="0,2"/>
                <TextBlock Text="• Better integration with ListClock design" Margin="0,2"/>
                <TextBlock Text="• Consistent with HandyControl library standards" Margin="0,2"/>
                <TextBlock Text="• Improved user experience with visual feedback" Margin="0,2"/>
                
                <TextBlock Text="🎉 ListClock now has sleek, modern scrollbars!" 
                           FontWeight="Bold" FontSize="14" Foreground="DarkGreen" Margin="0,15,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
