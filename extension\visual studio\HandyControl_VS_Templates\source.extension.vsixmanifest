﻿<?xml version="1.0" ?>
<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
    <Metadata>
        <Identity Id="1B6729DF-3360-48C4-86A9-68A0591FBBEB" Version="3.3.0" Language="en-US" Publisher="HandyOrg" />
        <DisplayName>HandyControl VS Templates</DisplayName>
        <Description xml:space="preserve">This VSIX Project will help people initialize a HandyControl project.</Description>
        <MoreInfo>https://handyorg.github.io/handycontrol</MoreInfo>
        <License>License.txt</License>
        <GettingStartedGuide>https://handyorg.github.io/handycontrol/quick_start</GettingStartedGuide>
        <ReleaseNotes>https://github.com/HandyOrg/HandyControl/releases</ReleaseNotes>
        <Icon>icon_100_100.png</Icon>
        <PreviewImage>icon_300_300.png</PreviewImage>
        <Tags>WPF C# Control</Tags>
    </Metadata>
    <Installation>
        <InstallationTarget Id="Microsoft.VisualStudio.Community" Version="[16.0, 18.0)">
            <ProductArchitecture>x86</ProductArchitecture>
        </InstallationTarget>
        <InstallationTarget Id="Microsoft.VisualStudio.Enterprise" Version="[16.0, 18.0)">
            <ProductArchitecture>x86</ProductArchitecture>
        </InstallationTarget>
        <InstallationTarget Id="Microsoft.VisualStudio.Pro" Version="[16.0, 18.0)">
            <ProductArchitecture>x86</ProductArchitecture>
        </InstallationTarget>
    </Installation>
    <Dependencies>
        <Dependency Id="Microsoft.Framework.NDP" DisplayName="Microsoft .NET Framework" d:Source="Manual" Version="[4.0,)" />
    </Dependencies>
    <Prerequisites>
        <Prerequisite Id="Microsoft.VisualStudio.Component.CoreEditor" Version="[16.0, 18.0)" DisplayName="Visual Studio core editor" />
    </Prerequisites>
    <Assets>
        <Asset Type="Microsoft.VisualStudio.ProjectTemplate" d:Source="Project" d:ProjectName="WpfApp" d:TargetPath="|WpfApp;TemplateProjectOutputGroup|" Path="ProjectTemplates" d:VsixSubPath="ProjectTemplates" />
        <Asset Type="Microsoft.VisualStudio.ProjectTemplate" d:Source="Project" d:ProjectName="WpfCoreApp" d:TargetPath="|WpfCoreApp;TemplateProjectOutputGroup|" Path="ProjectTemplates" d:VsixSubPath="ProjectTemplates" />
    </Assets>
</PackageManifest>
