.post-body .note {
  note_icons = hexo-config('note.icons');
  note_style = hexo-config('note.style');

  position:             relative;
  padding:              15px;
  margin-bottom:        20px;

  if note_style == 'simple' {
    border:             1px solid $gainsboro;
    border-left-width:  5px;
  }
  if note_style == 'modern' {
    border:             1px solid transparent;
    background-color:   $whitesmoke;
  }
  if note_style == 'flat' {
    border:             initial;
    border-left:        3px solid $gainsboro;
    background-color:   lighten($gainsboro, 65%);
  }
  border-radius:        unit(hexo-config('note.border_radius'), px) if hexo-config('note.border_radius') is a 'unit';

  h2, h3, h4, h5, h6 {
    if note_icons {
      margin-top:       3px;
    } else {
      margin-top:       0;
    }
    margin-bottom:      0;
    border-bottom:      initial;
    padding-top:        0 !important;
  }

  p, ul, ol, table, pre, blockquote {
    &:first-child {
      margin-top:       0;
    }
    &:last-child {
      margin-bottom:    0;
    }
  }

  if note_icons {
    &:not(.no-icon) {
      padding-left:     45px;
      &:before {
        position:       absolute;
        font-family:    $font-family-icons;
        font-size:      larger;
        top:            13px;
        left:           15px;
      }
    }
  }

  &.default {
    if note_style == 'flat' {
      background-color: $note-default-bg;
    }
    if note_style == 'modern' {
      background-color: $note-modern-default-bg;
      border-color:     $note-modern-default-border;
      color:            $note-modern-default-text;
      a, span.exturl {
        &:not(.btn) {
          color:                     $note-modern-default-text;
          border-bottom:   1px solid $note-modern-default-text;
          &:hover {
            color:                   $note-modern-default-hover;
            border-bottom: 1px solid $note-modern-default-hover;
          }
        }
      }
    }
    if not note_style == 'modern' {
      border-left-color:   $note-default-border;
      h2, h3, h4, h5, h6 {
        color:             $note-default-text;
      }
    }
    if note_icons {
      &:not(.no-icon) {
        &:before {
          content:         $note-default-icon;
          if not note_style == 'modern' {
            color:         $note-default-text;
          }
        }
      }
    }
  }

  &.primary {
    if note_style == 'flat' {
      background-color:    $note-primary-bg;
    }
    if note_style == 'modern' {
      background-color:    $note-modern-primary-bg;
      border-color:        $note-modern-primary-border;
      color:               $note-modern-primary-text;
      a, span.exturl {
        &:not(.btn) {
          color:                     $note-modern-primary-text;
          border-bottom:   1px solid $note-modern-primary-text;
          &:hover {
            color:                   $note-modern-primary-hover;
            border-bottom: 1px solid $note-modern-primary-hover;
          }
        }
      }
    }
    if not note_style == 'modern' {
      border-left-color:   $note-primary-border;
      h2, h3, h4, h5, h6 {
        color:             $note-primary-text;
      }
    }
    if note_icons {
      &:not(.no-icon) {
        &:before {
          content:         $note-primary-icon;
          if not note_style == 'modern' {
            color :        $note-primary-text;
          }
        }
      }
    }
  }

  &.info {
    if note_style == 'flat' {
      background-color:    $note-info-bg;
    }
    if note_style == 'modern' {
      background-color:    $note-modern-info-bg;
      border-color:        $note-modern-info-border;
      color:               $note-modern-info-text;
      a, span.exturl {
        &:not(.btn) {
          color:                     $note-modern-info-text;
          border-bottom:   1px solid $note-modern-info-text;
          &:hover {
            color:                   $note-modern-info-hover;
            border-bottom: 1px solid $note-modern-info-hover;
          }
        }
      }
    }
    if not note_style == 'modern' {
      border-left-color:   $note-info-border;
      h2, h3, h4, h5, h6 {
        color:             $note-info-text;
      }
    }
    if note_icons {
      &:not(.no-icon) {
        &:before {
          content:         $note-info-icon;
          if not note_style == 'modern' {
            color :        $note-info-text;
          }
        }
      }
    }
  }

  &.success {
    if note_style == 'flat' {
      background-color:    $note-success-bg;
    }
    if note_style == 'modern' {
      background-color:    $note-modern-success-bg;
      border-color:        $note-modern-success-border;
      color:               $note-modern-success-text;
      a, span.exturl {
        &:not(.btn) {
          color:                     $note-modern-success-text;
          border-bottom:   1px solid $note-modern-success-text;
          &:hover {
            color:                   $note-modern-success-hover;
            border-bottom: 1px solid $note-modern-success-hover;
          }
        }
      }
    }
    if not note_style == 'modern' {
      border-left-color:   $note-success-border;
      h2, h3, h4, h5, h6 {
        color:             $note-success-text;
      }
    }
    if note_icons {
      &:not(.no-icon) {
        &:before {
          content:         $note-success-icon;
          if not note_style == 'modern' {
            color :        $note-success-text;
          }        
        }
      }
    }
  }

  &.warning {
    if note_style == 'flat' {
      background-color:    $note-warning-bg;
    }
    if note_style == 'modern' {
      background-color:    $note-modern-warning-bg;
      border-color:        $note-modern-warning-border;
      color:               $note-modern-warning-text;
      a, span.exturl {
        &:not(.btn) {
          color:                     $note-modern-warning-text;
          border-bottom:   1px solid $note-modern-warning-text;
          &:hover {
            color:                   $note-modern-warning-hover;
            border-bottom: 1px solid $note-modern-warning-hover;
          }
        }
      }
    }
    if not note_style == 'modern' {
      border-left-color:   $note-warning-border;
      h2, h3, h4, h5, h6 {
        color:             $note-warning-text;
      }
    }
    if note_icons {
      &:not(.no-icon) {
        &:before {
          content:         $note-warning-icon;
          if not note_style == 'modern' {
            color :        $note-warning-text;
          }
        }
      }
    }
  }

  &.danger {
    if note_style == 'flat' {
      background-color:    $note-danger-bg;
    }
    if note_style == 'modern' {
      background-color:    $note-modern-danger-bg;
      border-color:        $note-modern-danger-border;
      color:               $note-modern-danger-text;
      a, span.exturl {
        &:not(.btn) {
          color:                     $note-modern-danger-text;
          border-bottom:   1px solid $note-modern-danger-text;
          &:hover {
            color:                   $note-modern-danger-hover;
            border-bottom: 1px solid $note-modern-danger-hover;
          }
        }
      }
    }
    if not note_style == 'modern' {
      border-left-color:   $note-danger-border;
      h2, h3, h4, h5, h6 {
        color:             $note-danger-text;
      }
    }
    if note_icons {
      &:not(.no-icon) {
        &:before {
          content:         $note-danger-icon;
          if not note_style == 'modern' {
            color :        $note-danger-text;
          }
        }
      }
    }
  }
}
