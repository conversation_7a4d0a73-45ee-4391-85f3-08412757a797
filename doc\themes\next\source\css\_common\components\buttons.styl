.btn {
  display: inline-block;
  padding: 0 20px;
  font-size: $btn-default-font-size;
  color: $btn-default-color;
  background: $btn-default-bg;
  border: $btn-default-border-width solid $btn-default-border-color;
  text-decoration: none;
  border-radius: $btn-default-radius;
  transition-property: background-color;
  the-transition();
  line-height: 2;

  &:hover {
    border-color: $btn-default-hover-border-color;
    color: $btn-default-hover-color;
    background: $btn-default-hover-bg;
  }

  +.btn {
    margin: 0 0 8px 8px;
  }

  .fa-fw {
    width: (18em / 14);
    text-align: left;
  }
}

.btn-bar {
  show();
  width: 22px;
  height: 2px;
  background: $text-color;
  border-radius: 1px;

  &+.btn-bar { margin-top: 4px; }
}
