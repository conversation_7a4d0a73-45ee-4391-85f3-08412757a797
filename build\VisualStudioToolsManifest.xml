<FileList>
  <File Reference="HandyControl.dll">
    <ToolboxItems UIFramework="WPF" VSCategory="HandyControl" BlendCategory="HandyControl">
      <Item Type="HandyControl.Controls.AnimationPath" />
      <Item Type="HandyControl.Expression.Shapes.Arc" />
      <Item Type="HandyControl.Controls.AxleCanvas" />
      <Item Type="HandyControl.Controls.Badge" />
      <Item Type="HandyControl.Controls.BlendEffectBox" />
      <Item Type="HandyControl.Controls.ButtonGroup" />
      <Item Type="HandyControl.Controls.CalendarWithClock" />
      <Item Type="HandyControl.Controls.Card" />
      <Item Type="HandyControl.Controls.Carousel" />
      <Item Type="HandyControl.Controls.ChatBubble" />
      <Item Type="HandyControl.Controls.CheckComboBox" />
      <Item Type="HandyControl.Controls.CirclePanel" />
      <Item Type="HandyControl.Controls.CircleProgressBar" />
      <Item Type="HandyControl.Controls.Clock" />
      <Item Type="HandyControl.Controls.Col" />
      <Item Type="HandyControl.Controls.ColorPicker" />
      <Item Type="HandyControl.Controls.ComboBox" />
      <Item Type="HandyControl.Controls.CompareSlider" />
      <Item Type="HandyControl.Controls.ContextMenuButton" />
      <Item Type="HandyControl.Controls.ContextMenuToggleButton" />
      <Item Type="HandyControl.Controls.CoverFlow" />
      <Item Type="HandyControl.Controls.CoverView" />
      <Item Type="HandyControl.Controls.DashedBorder" />
      <Item Type="HandyControl.Controls.DatePicker" />
      <Item Type="HandyControl.Controls.DateTimePicker" />
      <Item Type="HandyControl.Controls.DialogContainer" />
      <Item Type="HandyControl.Controls.Divider" />
      <Item Type="HandyControl.Controls.Drawer" />
      <Item Type="HandyControl.Controls.Empty" />
      <Item Type="HandyControl.Controls.FlexPanel" />
      <Item Type="HandyControl.Controls.FlipClock" />
      <Item Type="HandyControl.Controls.FloatingBlock" />
      <Item Type="HandyControl.Controls.GifImage" />
      <Item Type="HandyControl.Controls.GotoTop" />
      <Item Type="HandyControl.Controls.Gravatar" />
      <Item Type="HandyControl.Controls.HoneycombPanel" />
      <Item Type="HandyControl.Controls.ImageBlock" />
      <Item Type="HandyControl.Controls.ImageSelector" />
      <Item Type="HandyControl.Controls.ImageViewer" />
      <Item Type="HandyControl.Controls.ListClock" />
      <Item Type="HandyControl.Controls.LoadingCircle" />
      <Item Type="HandyControl.Controls.LoadingLine" />
      <Item Type="HandyControl.Controls.Magnifier" />
      <Item Type="HandyControl.Controls.NotifyIcon" />
      <Item Type="HandyControl.Controls.NumericUpDown" />
      <Item Type="HandyControl.Controls.OutlineText" />
      <Item Type="HandyControl.Controls.Pagination" />
      <Item Type="HandyControl.Controls.PasswordBox" />
      <Item Type="HandyControl.Controls.PinBox" />
      <Item Type="HandyControl.Controls.Poptip" />
      <Item Type="HandyControl.Controls.PreviewSlider" />
      <Item Type="HandyControl.Controls.ProgressButton" />
      <Item Type="HandyControl.Controls.PropertyGrid" />
      <Item Type="HandyControl.Controls.RangeSlider" />
      <Item Type="HandyControl.Controls.Rate" />
      <Item Type="HandyControl.Controls.RelativePanel" />
      <Item Type="HandyControl.Controls.Row" />
      <Item Type="HandyControl.Controls.RunningBlock" />
      <Item Type="HandyControl.Controls.ScrollViewer" />
      <Item Type="HandyControl.Controls.SearchBar" />
      <Item Type="HandyControl.Controls.Shield" />
      <Item Type="HandyControl.Controls.SideMenu" />
      <Item Type="HandyControl.Controls.SimplePanel" />
      <Item Type="HandyControl.Controls.SimpleStackPanel" />
      <Item Type="HandyControl.Controls.SimpleText" />
      <Item Type="HandyControl.Controls.SplitButton" />
      <Item Type="HandyControl.Controls.StepBar" />
      <Item Type="HandyControl.Controls.TabControl" />
      <Item Type="HandyControl.Controls.Tag" />
      <Item Type="HandyControl.Controls.TagContainer" />
      <Item Type="HandyControl.Controls.TextBox" />
      <Item Type="HandyControl.Controls.TimeBar" />
      <Item Type="HandyControl.Controls.TimePicker" />
      <Item Type="HandyControl.Controls.ToggleBlock" />
      <Item Type="HandyControl.Controls.Transfer" />
      <Item Type="HandyControl.Controls.TransitioningContentControl" />
      <Item Type="HandyControl.Controls.UniformSpacingPanel" />
      <Item Type="HandyControl.Controls.WaterfallPanel" />
      <Item Type="HandyControl.Controls.Watermark" />
      <Item Type="HandyControl.Controls.WaveProgressBar" />
    </ToolboxItems>
  </File>
</FileList>