.sidebar-toggle {
  &:hover .sidebar-toggle-line {
    background: $sidebar-highlight;
  }
  position: fixed;
  right: $b2t-position-right;
  bottom: 45px;
  width: 14px;
  height: 14px;
  padding: 5px;
  background: $black-deep;
  line-height: 0;
  z-index: $zindex-5;
  cursor: pointer;

  +tablet-mobile() {
    hexo-config('sidebar.onmobile') ? fixbutton() : hide();
  }
}

.sidebar-toggle-line {
  position: relative;
  display: inline-block;
  vertical-align: top;
  height: 2px;
  width: 100%;
  background: white;
  margin-top: 3px;

  &:first-child { margin-top: 0; }
}
