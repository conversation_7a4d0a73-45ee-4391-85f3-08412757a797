@import "../Pisces/_layout";
@import "../Pisces/_brand";
@import "../Pisces/_menu";
@import "../Pisces/_sub-menu";
@import "../Pisces/_sidebar";

// =================================================
// Rewrite _layout.styl
// =================================================
// Sidebar padding used as main desktop content padding for sidebar padding and post blocks padding too.

// In `source/css/_variables/Pisces.styl` there are variable for main offset:
// $sidebar-offset                   = 12px;
// This value alse can be changed in main NexT config as `sidebar: offset: 12` option.

// In `source/css/_variables/base.styl` there are variables for other resolutions:
// $content-tablet-padding           = 10px;
// $content-mobile-padding           = 8px;
// P.S. If u want to change this paddings u may set this variables into `source/css/_variables/custom.styl`.

// So, it will 12px in Desktop, 10px in Tablets and 8px in Mobiles for all possible paddings.
// =================================================
// Read values from NexT config and set they as local variables to use as string variables (in any CSS section).
use_seo = hexo-config('seo');

// =================================================
// Desktop layout styles.
// =================================================
// Post blocks.
.content-wrap {
  padding: initial;
  background: initial;
  box-shadow: initial;
  border-radius: initial;
}

// Post & Comments blocks.
.post-block {
  padding: $content-desktop-padding;
  background: white;
  box-shadow: $box-shadow-inner;
  border-radius: $border-radius-inner;
}

// When blocks are siblings (homepage).
#posts > article + article {
  .post-block {
    margin-top: $sidebar-offset;
    // Rewrite shadows & borders because all blocks have offsets.
    box-shadow: $box-shadow;
    border-radius: $border-radius;
  }
}

// Comments blocks.
.comments {
  padding: $content-desktop-padding;
  margin: auto;
  margin-top: $sidebar-offset;
  background: white;
  box-shadow: $box-shadow;
  border-radius: $border-radius;
}

// Top main padding from header to posts (default 40px).
.posts-expand {
  padding-top: initial;
}

// Post navigation items.
.post-nav-divider {
  width: 4%;
}
.post-nav-item {
  width: 48%;
}

// Post delimiters.
.post-eof {
  hide();
}

// Pagination.
.pagination {
  .prev, .next, .page-number {
    margin-bottom: initial;
    top: initial;
  }
  margin: $sidebar-offset 0 0;
  border-top: initial;
  background: white;
  box-shadow: $box-shadow;
  border-radius: $border-radius;
  padding: 10px 0 10px;
}

// Footer alignment.
.main {
  padding-bottom: initial;
}
.footer {
  bottom: auto;
}

// Sub-menu(s).
.sub-menu {
  border-bottom: initial !important;
  box-shadow: $box-shadow-inner;
  // Adapt submenu(s) with post-blocks.
  &+ #content > #posts {
    .post-block {
      box-shadow: $box-shadow;
      margin-top: $sidebar-offset;
      +tablet() {
        margin-top: $content-tablet-padding;
      }
      +mobile() {
        margin-top: $content-mobile-padding;
      }
    }
  }
}

// =================================================
// Headers.
// =================================================
// No need anymore?
.post-header {
  h1, h2 {
    margin: initial;
  }
}
.posts-expand .post-title-link {
  line-height: inherit;
}
.posts-expand .post-title {
  font-size: 1.7em;
}
.post-body {
  h1 {
    font-size: 1.6em;
    border-bottom: 1px solid $body-bg-color;
    code {
      font-size: 1em;
    }
  }
  h2 {
    font-size: 1.45em;
    border-bottom: 1px solid $body-bg-color;
    code {
      font-size: 1em;
    }
  }
  h3 {
    font-size: 1.3em;
    code {
      font-size: 1em;
    }
    if use_seo {
      border-bottom: 1px solid $body-bg-color;
    } else {
      border-bottom: 1px dotted $body-bg-color;
    }
  }
  h4 {
    font-size: 1.2em;
    code {
      font-size: 1em;
    }
    if use_seo {
      border-bottom: 1px dotted $body-bg-color;
    }
  }
  h5 {
    font-size: 1.07em;
    code {
      font-size: 1em;
    }
  }
  h6 {
    font-size: 1.03em;
    code {
      font-size: 1em;
    }
  }
}

// =================================================
// > 768px & < 991px
// =================================================
+tablet() {

  // Posts in blocks.
  .content-wrap {
    padding: $content-tablet-padding;
  }
  .posts-expand {
    margin: initial;

    // Components inside Posts.
    .post-button {
      margin-top: ($content-tablet-padding * 2);
    }
  }

  .post-block {
    // Inside posts blocks content padding (default 40px).
    padding: ($content-tablet-padding * 2);
    // Rewrite shadows & borders because all blocks have offsets.
    box-shadow: $box-shadow;
    border-radius: $border-radius;
  }

  // Only if blocks are siblings need bottom margin (homepage).
  #posts > article + article {
    .post-block {
      margin-top: $content-tablet-padding;
    }
  }

  .comments {
    margin-top: $content-tablet-padding;
    padding: $content-tablet-padding ($content-tablet-padding * 2);
    //padding: initial;
    //padding-top: $content-tablet-padding;
  }

  .pagination {
    margin: $content-tablet-padding 0 0;
  }

}
// =================================================
// < 767px
// =================================================
+mobile() {

  // Posts in blocks.
  .content-wrap {
    padding: $content-mobile-padding;
  }
  .posts-expand {
    margin: initial;

    // Components inside Posts.
    .post-button {
      margin: $sidebar-offset 0px;
    }
    img {
      padding: initial !important;
    }
  }

  .post-block {
    // Inside posts blocks content padding (default 40px).
    padding: $sidebar-offset;
    min-height: auto;
    // Rewrite shadows & borders because all blocks have offsets.
    box-shadow: $box-shadow;
    border-radius: $border-radius;
  }

  // Only if blocks are siblings need bottom margin (homepage).
  #posts > article + article {
    .post-block {
      margin-top: $content-mobile-padding;
    }
  }

  .comments {
    margin-top: $content-mobile-padding;
    padding: 0 $sidebar-offset;
  }

  .pagination {
    margin: $content-mobile-padding 0 0;
  }
}
