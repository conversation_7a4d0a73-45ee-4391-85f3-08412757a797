using System;
using System.Windows;
using System.Windows.Controls;
using StandaloneTimePicker.Data;

namespace StandaloneTimePicker.Controls.Clock
{
    /// <summary>
    /// Base class for clock controls
    /// </summary>
    [TemplatePart(Name = ElementButtonConfirm, Type = typeof(Button))]
    public abstract class ClockBase : Control
    {
        protected const string ElementButtonConfirm = "PART_ButtonConfirm";

        protected Button ButtonConfirm;
        protected bool AppliedTemplate;

        /// <summary>
        /// Event raised when the confirm button is clicked
        /// </summary>
        public event Action Confirmed;

        /// <summary>
        /// Event raised when the display time changes
        /// </summary>
        public event EventHandler<FunctionEventArgs<DateTime>> DisplayTimeChanged;

        /// <summary>
        /// Routed event for selected time changes
        /// </summary>
        public static readonly RoutedEvent SelectedTimeChangedEvent =
            EventManager.RegisterRoutedEvent("SelectedTimeChanged", RoutingStrategy.Direct,
                typeof(EventHandler<FunctionEventArgs<DateTime?>>), typeof(ClockBase));

        /// <summary>
        /// Event raised when the selected time changes
        /// </summary>
        public event EventHandler<FunctionEventArgs<DateTime?>> SelectedTimeChanged
        {
            add => AddHandler(SelectedTimeChangedEvent, value);
            remove => RemoveHandler(SelectedTimeChangedEvent, value);
        }

        /// <summary>
        /// Dependency property for ShowConfirmButton
        /// </summary>
        public static readonly DependencyProperty ShowConfirmButtonProperty = DependencyProperty.Register(
            nameof(ShowConfirmButton), typeof(bool), typeof(ClockBase), new PropertyMetadata(ValueBoxes.FalseBox));

        /// <summary>
        /// Gets or sets whether to show the confirm button
        /// </summary>
        public bool ShowConfirmButton
        {
            get => (bool)GetValue(ShowConfirmButtonProperty);
            set => SetValue(ShowConfirmButtonProperty, ValueBoxes.BooleanBox(value));
        }

        /// <summary>
        /// Dependency property for SelectedTime
        /// </summary>
        public static readonly DependencyProperty SelectedTimeProperty = DependencyProperty.Register(
            nameof(SelectedTime), typeof(DateTime?), typeof(ClockBase),
            new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedTimeChanged));

        private static void OnSelectedTimeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var ctl = (ClockBase)d;
            var v = (DateTime?)e.NewValue;
            ctl.DisplayTime = v ?? DateTime.Now;
            ctl.OnSelectedTimeChanged(new FunctionEventArgs<DateTime?>(SelectedTimeChangedEvent, ctl)
            {
                Info = v
            });
        }

        /// <summary>
        /// Gets or sets the selected time
        /// </summary>
        public DateTime? SelectedTime
        {
            get => (DateTime?)GetValue(SelectedTimeProperty);
            set => SetValue(SelectedTimeProperty, value);
        }

        /// <summary>
        /// Dependency property for DisplayTime
        /// </summary>
        public static readonly DependencyProperty DisplayTimeProperty = DependencyProperty.Register(
            nameof(DisplayTime), typeof(DateTime), typeof(ClockBase),
            new FrameworkPropertyMetadata(DateTime.Now, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault,
                OnDisplayTimeChanged));

        private static void OnDisplayTimeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var ctl = (ClockBase)d;
            ctl.OnDisplayTimeChanged(new FunctionEventArgs<DateTime>(ctl)
            {
                Info = (DateTime)e.NewValue
            });
        }

        /// <summary>
        /// Gets or sets the display time
        /// </summary>
        public DateTime DisplayTime
        {
            get => (DateTime)GetValue(DisplayTimeProperty);
            set => SetValue(DisplayTimeProperty, value);
        }

        /// <summary>
        /// Dependency property for TimeFormat
        /// </summary>
        public static readonly DependencyProperty TimeFormatProperty = DependencyProperty.Register(
            nameof(TimeFormat), typeof(string), typeof(ClockBase), new PropertyMetadata("HH:mm:ss"));

        /// <summary>
        /// Gets or sets the time format string
        /// </summary>
        public string TimeFormat
        {
            get => (string)GetValue(TimeFormatProperty);
            set => SetValue(TimeFormatProperty, value);
        }

        /// <summary>
        /// Called when the selected time changes
        /// </summary>
        /// <param name="e">Event arguments</param>
        protected virtual void OnSelectedTimeChanged(FunctionEventArgs<DateTime?> e) => RaiseEvent(e);

        /// <summary>
        /// Called when the display time changes
        /// </summary>
        /// <param name="e">Event arguments</param>
        protected virtual void OnDisplayTimeChanged(FunctionEventArgs<DateTime> e)
        {
            var handler = DisplayTimeChanged;
            handler?.Invoke(this, e);
        }

        /// <summary>
        /// Handles the confirm button click
        /// </summary>
        /// <param name="sender">Event sender</param>
        /// <param name="e">Event arguments</param>
        protected void ButtonConfirm_OnClick(object sender, RoutedEventArgs e)
        {
            SelectedTime = DisplayTime;
            Confirmed?.Invoke();
        }

        /// <summary>
        /// Updates the clock with the specified time
        /// </summary>
        /// <param name="time">The time to update to</param>
        internal abstract void Update(DateTime time);

        /// <summary>
        /// Called when the clock is closed
        /// </summary>
        public virtual void OnClockClosed()
        {
        }

        /// <summary>
        /// Called when the clock is opened
        /// </summary>
        public virtual void OnClockOpened()
        {
        }
    }
}
