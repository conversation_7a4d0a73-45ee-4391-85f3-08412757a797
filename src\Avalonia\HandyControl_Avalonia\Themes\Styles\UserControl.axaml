﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
	<ControlTheme x:Key="{x:Type UserControl}" TargetType="UserControl">
        <Setter Property="Template">
            <ControlTemplate TargetType="UserControl">
                <ContentPresenter
                    Name="PART_ContentPresenter"
                    Padding="{TemplateBinding Padding}"
                    HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    Content="{TemplateBinding Content}"
                    ContentTemplate="{TemplateBinding ContentTemplate}"
                    CornerRadius="{TemplateBinding CornerRadius}" />
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
