.posts-expand .post-title {
  word-wrap();
  text-align: center;
  font-weight: $posts-expand-title-font-weight;

  if hexo-config('post_edit.enable') {
    .post-edit-link {
      color: #bbb;
      display: inline-block;
      float: right;
      border-bottom: none;
      the-transition-ease-in();
      margin-left: -1.2em;
      +mobile-small() {
        margin-left: initial;
      }
      &:hover {
        color: black;
      }
    }
  }
}

.posts-expand .post-title-link {
  display: inline-block;
  position: relative;
  color: $black-light;
  border-bottom: none;
  line-height: 1.2;
  vertical-align: top;

  &::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: #000;
    visibility: hidden;
    transform: scaleX(0);
    the-transition();
  }

  &:hover::before {
    visibility: visible;
    transform: scaleX(1);
  }

  .fa {
    font-size: 20px;
    margin-left: 5px;
  }
}
