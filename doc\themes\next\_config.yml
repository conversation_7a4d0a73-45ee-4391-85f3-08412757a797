# ---------------------------------------------------------------
# Theme Core Configuration Settings
# See: https://theme-next.org/docs/theme-settings/
# ---------------------------------------------------------------

# If false, merge configs from `_data/next.yml` into default configuration (rewrite).
# If true, will fully override default configuration by options from `_data/next.yml` (override). Only for NexT settings.
# And if true, all config from default NexT `_config.yml` must be copied into `next.yml`. Use if you know what you are doing.
# Useful if you want to comment some options from NexT `_config.yml` by `next.yml` without editing default config.
override: false

# Allow to cache content generation. Introduced in NexT v6.0.0.
cache:
  enable: true

# Redefine custom file paths. Introduced in NexT v6.0.2. If commented, will be used default custom file paths.
# For example, you want to put your custom styles file outside theme directory in root `source/_data`, set `styles: source/_data/styles.styl`
#custom_file_path:
  # Default paths: layout/_custom/*
  #head: source/_data/head.swig
  #header: source/_data/header.swig
  #sidebar: source/_data/sidebar.swig

  # Default path: source/css/_variables/custom.styl
  #variables: source/_data/variables.styl
  # Default path: source/css/_mixins/custom.styl
  #mixins: source/_data/mixins.styl
  # Default path: source/css/_custom/custom.styl
  #styles: source/_data/styles.styl


# ---------------------------------------------------------------
# Site Information Settings
# See: https://theme-next.org/docs/getting-started/
# ---------------------------------------------------------------

favicon:
  small: /images/favicon-16x16-HandyOrg.png
  medium: /images/favicon-32x32-HandyOrg.png
  apple_touch_icon: /images/apple-touch-icon-HandyOrg.png
  safari_pinned_tab: /images/logo.svg
  #android_manifest: /images/manifest.json
  #ms_browserconfig: /images/browserconfig.xml

# Set rss to false to disable feed link.
# Leave rss as blank to use site's feed link, and install hexo-generator-feed: `npm install hexo-generator-feed --save`.
# Set rss to specific value if you have burned your feed already.
rss:

footer:
  # Specify the date when the site was setup. If not defined, current year will be used.
  #since: 2015

  # Icon between year and copyright info.
  icon:
    # Icon name in fontawesome, see: https://fontawesome.com/v4.7.0/icons/
    # `heart` is recommended with animation in red (#ff0000).
    name: HandyOrg
    # If you want to animate the icon, set it to true.
    animated: false
    # Change the color of icon, using Hex Code.
    color: "#808080"

  # If not defined, `author` from Hexo main config will be used.
  copyright:

  powered:
    # Hexo link (Powered by Hexo).
    enable: false
    # Version info of Hexo after Hexo link (vX.X.X).
    version: true

  theme:
    # Theme & scheme info link (Theme - NexT.scheme).
    enable: true
    # Version info of NexT after scheme info (vX.X.X).
    version: true

  # Beian icp information for Chinese users. In China, every legal website should have a beian icp in website footer.
  # http://www.miitbeian.gov.cn
  beian:
    enable: false
    icp:

  # Any custom text can be defined here.
  #custom_text: Hosted by <a href="https://pages.coding.me" class="theme-link" rel="noopener" target="_blank">Coding Pages</a>

# Creative Commons 4.0 International License.
# See: https://creativecommons.org/share-your-work/licensing-types-examples
# Available values of license: by | by-nc | by-nc-nd | by-nc-sa | by-nd | by-sa | zero
# You can set a language value if you prefer a translated version of CC license.
# CC licenses are available in 39 languages, where you can find the specific and correct abbreviation you need.
# Valid values of language: deed.zh, deed.fr, deed.de, etc.
creative_commons:
  license: by-nc-sa
  sidebar: false
  post: false
  language:

# `Follow me on GitHub` banner in the top-right corner.
github_banner:
  enable: true
  permalink: https://github.com/HandyOrg
  title: Follow us on GitHub


# ---------------------------------------------------------------
# SEO Settings
# ---------------------------------------------------------------

# Disable Baidu transformation on mobile devices.
disable_baidu_transformation: false

# Set a canonical link tag in your hexo, you could use it for your SEO of blog.
# See: https://support.google.com/webmasters/answer/139066
# Tips: Before you open this tag, remember set up your URL in hexo _config.yml (e.g. url: http://yourdomain.com)
canonical: true

# Change headers hierarchy on site-subtitle (will be main site description) and on all post / page titles for better SEO-optimization.
seo: false

# If true, will add site-subtitle to index page, added in main hexo config.
# subtitle: Subtitle
index_with_subtitle: false

# Automatically add external URL with BASE64 encrypt & decrypt.
exturl: false

# Google Webmaster tools verification.
# See: https://www.google.com/webmasters
#google_site_verification:

# Bing Webmaster tools verification.
# See: https://www.bing.com/webmaster
#bing_site_verification:

# Yandex Webmaster tools verification.
# See: https://webmaster.yandex.ru
#yandex_site_verification:

# Baidu Webmaster tools verification.
# See: https://ziyuan.baidu.com/site
#baidu_site_verification:

# Enable baidu push so that the blog will push the url to baidu automatically which is very helpful for SEO.
baidu_push: false


# ---------------------------------------------------------------
# Menu Settings
# ---------------------------------------------------------------

# When running the site in a subdirectory (e.g. domain.tld/blog), remove the leading slash from link value (/archives -> archives).
# Usage: `Key: /link/ || icon`
# Key is the name of menu item. If the translation for this item is available, the translated text will be loaded, otherwise the Key name will be used. Key is case-senstive.
# Value before `||` delimeter is the target link.
# Value after `||` delimeter is the name of FontAwesome icon. If icon (with or without delimeter) is not specified, question icon will be loaded.
# External url should start with http:// or https://
menu:
  主页: / || 
  HandyControl - 3.0.0:
    default: /handycontrol/ ||
    鸣谢: /tnx/ ||
    快速开始: /quick_start/ ||
    基础xaml定义: 
      default: /basic_xaml/ ||
      行为: /behaviors/ ||
      画刷: /brushes/ ||
      颜色: /colors/ ||
      转换器: /converters/ ||
      效果: /effects/ ||
      字体大小: /fonts/ ||
      几何形状: /geometries/ ||
      Path路径: /paths/ ||
      尺寸: /sizes/ ||
    附加属性:
      default: /attach/ ||
      BackgroundSwitchElement 可切换背景的元素: /backgroundSwitchElement/ ||
      BorderElement 具有边框的元素: /borderElement/ ||
      DataGridAttach DataGrid专用: /dataGridAttach/ ||
      DropDownElement 可下拉内容的元素: /dropDownElement/ ||
      EdgeElement 具有边界内容的元素: /edgeElement/ ||
      IconElement 具有图标的元素: /iconElement/ ||
      IconSwitchElement 可切换图标的元素: /iconSwitchElement/ ||
      ImageAttach Image专用: /imageAttach/ ||
      InfoElement 信息元素: /infoElement/ ||
      PanelElement 面板元素: /panelElement/ ||
      PasswordBoxAttach 密码框专用: /passwordBoxAttach/ ||
      RectangleAttach Rectangle专用: /rectangleAttach/ ||
      StatusSwitchElement 可切换状态的元素: /statusSwitchElement/ ||
      TextBlockAttach 文本块专用: /textBlockAttach/ ||
      TipElement 提示元素: /tipElement/ ||
      TitleElement 标题元素: /titleElement/ ||
      VisualElement 可视化元素: /visualElement/ ||
      WindowAttach 窗体专用: /windowAttach/ ||
    原生控件:
      default: /native_controls/ ||
      Border 边框: /border/ ||
      Button 按钮: /button/ ||
      Calendar 日历: /calendar/ ||
      CheckBox 复选框: /checkBox/ ||
      ComboBox 组合框: /comboBox/ ||
      ContentControl 内容控件: /contentControl/ ||
      ContextMenu 上下文菜单: /contextMenu/ ||
      DataGrid 数据表格: /dataGrid/ ||
      DatePicker 日期选择器: /datePicker/ ||
      Expander 展开框: /expander/ ||
      FlowDocument 流文档: /flowDocument/ ||
      Frame 导航框架: /frame/ ||
      GroupBox 分组框: /groupBox/ ||
      Image 图片: /image/ ||
      Label 标签: /label/ ||
      ListBox 列表框: /listBox/ ||
      ListView 列表视图: /listView/ ||
      Menu 菜单: /menu/ ||
      PasswordBox 密码框: /passwordBox/ ||
      ProgressBar 进度条: /progressBar/ ||
      RadioButton 单选按钮: /radioButton/ ||
      RichTextBox 富文本框: /richTextBox/ ||
      ScrollViewer 滚动视图: /scrollViewer/ ||
      Seperator 分隔符: /seperator/ ||
      Slider 滑块: /slider/ ||
      StatusBar 状态栏: /statusBar/ ||
      TabControl 选项卡控件: /tabControl/ ||
      TextBlock 文本块: /textBlock/ ||
      TextBox 文本框: /textBox/ ||
      ToggleButton 切换按钮: /toggleButton/ ||
      ToolBar 工具条: /toolBar/ ||
      ToolTip 工具提示: /toolTip/ ||
      TreeView 树视图: /treeView/ ||
      Window 窗口: /window/ ||
    扩展控件:
      default: /extend_controls/ ||
      AnimationPath 动画路径: /animationPath/ ||
      Badge 标记: /badge/ ||
      BlurWindow 背景模糊窗口: /blurWindow/ ||
      ButtonGroup 按钮组: /buttonGroup/ ||
      CalendarWithClock 带有时钟的日历: /calendarWithClock/ ||
      Card 卡片: /card/ ||
      Carousel 轮播: /carousel/ ||
      ChatBubble 对话气泡: /chatBubble/ ||
      CheckComboBox 复选组合框: /checkComboBox/ ||
      CirclePanel 圆形布局: /circlePanel/ ||
      CircleProgressBar 圆形进度条: /circleProgressBar/ ||
      Clock 时钟: /clock/ ||
      ColorPicker 颜色拾取器: /colorPicker/ ||
      ComboBox 组合框: /comboBox/ ||
      CompareSlider 对比滑块: /compareSlider/ ||
      ContextMenuButton 上下文菜单按钮: /contextMenuButton/ ||
      CoverFlow 封面流: /coverFlow/ ||
      CoverView 封面视图: /coverView/ ||
      DashedBorder 虚线边框: /dashedBorder/ ||
      DatePicker 日期选择器: /datePicker/ ||
      DateTimePicker 日期时间选择器: /dateTimePicker/ ||
      Dialog 对话框: /dialog/ ||
      Divider 分割线: /divider/ ||
      Drawer 抽屉: /drawer/ ||
      FlipClock 翻页时钟: /flipClock/ ||
      FloatingBlock 漂浮块: /floatingBlock/ ||
      GifImage Gif图片: /gifImage/ ||
      GlowWindow 辉光窗口: /glowWindow/ ||
      GotoTop 回到顶部: /gotoTop/ ||
      Gravatar 头像: /gravatar/ ||
      Grid 栅格: /grid/ ||
      Growl 信息通知: /growl/ ||
      HoneycombPanel 蜂窝布局: /honeycombPanel/ ||
      ImageBlock 图片块: /imageBlock/ ||
      ImageBrowser 图片浏览器: /imageBrowser/ ||
      ImageSelector 图片选择器: /imageSelector/ ||
      ImageViewer 图片视图: /imageViewer/ ||
      Loading 加载条: /loading/ ||
      Magnifier 放大镜: /magnifier/ ||
      MessageBox 消息对话框: /messageBox/ ||
      Notification 桌面通知: /notification/ ||
      NotifyIcon 托盘图标: /notifyIcon/ ||
      NumericUpDown 数值选择控件: /numericUpDown/ ||
      OutlineText 轮廓文本: /outlineText/ ||
      Pagination 页码条: /pagination/ ||
      PasswordBox 密码框: /passwordBox/ ||
      PinBox PIN码框: /pinBox/ ||
      PopTip 气泡提示: /popTip/ ||
      PopupWindow 弹出窗口: /popupWindow/ ||
      PreviewSlider 预览滑块: /previewSlider/ ||
      ProgressButton 进度按钮: /progressButton/ ||
      PropertyGrid 属性编辑器: /propertyGrid/ ||
      RangeSlider 范围滑块: /rangeSlider/ ||
      Rate 评分: /rate/ ||
      RelativePanel 相对布局: /relativePanel/ ||
      RunningBlock 滚动块: /runningBlock/ ||
      Screenshot 截图: /screenshot/ ||
      ScrollViewer 滚动视图: /scrollViewer/ ||
      SearchBar 搜索栏: /searchBar/ ||
      Shield 徽章: /shield/ ||
      SideMenu 侧边菜单: /sideMenu/ ||
      SimpleItemsControl 简单项目控件: /simpleItemsControl/ ||
      SimplePanel 简单面板: /simplePanel/ ||
      SimpleText 简单文本: /simpleText/ ||
      SplitButton 分割按钮: /splitButton/ ||
      Sprite 精灵: /sprite/ ||
      StepBar 步骤条: /stepBar/ ||
      TabControl 选项卡控件: /tabControl/ ||
      Tag 标签: /tag/ ||
      TextBox 文本框: /textBox/ ||
      TimeBar 时间条: /timeBar/ ||
      TimePicker 时间选择器: /timePicker/ ||
      ToggleBlock 切换块: /toggleBlock/ ||
      Transfer 穿梭框: /transfer/ ||
      TransitioningContentControl 内容过渡控件: /transitioningContentControl/ ||
      WaterfallPanel 瀑布流: /waterfallPanel/ ||
      Watermark 水印: /watermark/ ||
      WaveProgressBar 波形进度条: /waveProgressBar/ ||
      Window 窗口: /window/ ||
    数据:
      default: /data/ ||
      GrowlInfo: /GrowlInfo/ ||
      InfoType: /InfoType/ ||
    工具和扩展:
      default: /tools/ ||
      Effect 特效: /effect/ ||
      HatchBrushGenerator 阴影画笔生成器: /hatchBrushGenerator/ ||
      MorphingAnimation 变形动画: /morphingAnimation/ ||
    国际化:
      default: /langs/ ||
    破坏性更新:
      default: /breaking_changes/ ||

# Enable / Disable menu icons / item badges.
menu_settings:
  icons: false
  badges: true


# ---------------------------------------------------------------
# Scheme Settings
# ---------------------------------------------------------------

# Schemes
#scheme: Muse
#scheme: Mist
#scheme: Pisces
scheme: Gemini


# ---------------------------------------------------------------
# Sidebar Settings
# See: https://theme-next.org/docs/theme-settings/sidebar
# ---------------------------------------------------------------

# Posts / Categories / Tags in sidebar.
site_state: false

# Social Links
# Usage: `Key: permalink || icon`
# Key is the link label showing to end users.
# Value before `||` delimeter is the target permalink.
# Value after `||` delimeter is the name of FontAwesome icon. If icon (with or without delimeter) is not specified, globe icon will be loaded.
social:
  GitHub: https://github.com/HandyOrg || github
  E-Mail: mailto:<EMAIL> || envelope
  QQ群1: //shang.qq.com/wpa/qunwpa?idkey=a571e5553c9d41e49c4f22f3a8b2865451497a795ff281fedf3285def247efc1 || qq
  QQ群2: //shang.qq.com/wpa/qunwpa?idkey=5c18622a0f6ee07a6f33afa8cdb85b1f72ea50e878271dfcec919c76b55afee7 || qq
  Nuget: https://www.nuget.org/packages/HandyControl || code
  Gitter: https://gitter.im/HandyControl/Lobby || commenting
  Wiki-en: https://github.com/ghost1372/HandyControl/wiki || book
  Wiki-fr: https://github.com/TheKeytrap/HandyControl/wiki || book

social_icons:
  enable: true
  icons_only: false
  transition: false

# Blog rolls
links_icon: link
links_title: Links
links_layout: block
#links_layout: inline
links:
  #Title: http://example.com

# Sidebar Avatar
avatar:
  # In theme directory (source/images): /images/avatar.gif
  # In site directory (source/uploads): /uploads/avatar.gif
  # You can also use other linking images.
  url: https://avatars2.githubusercontent.com/u/17383395?s=460&v=4
  # If true, the avatar would be dispalyed in circle.
  rounded: false
  # The value of opacity should be choose from 0 to 1 to set the opacity of the avatar.
  opacity: 1
  # If true, the avatar would be rotated with the cursor.
  rotated: false

# Table Of Contents in the Sidebar
toc:
  enable: true
  # Automatically add list number to toc.
  number: true
  # If true, all words will placed on next lines if header width longer then sidebar width.
  wrap: false
  # If true, all level of TOC in a post will be displayed, rather than the activated part of it.
  expand_all: false
  # Maximum heading depth of generated toc. You can set it in one post through `toc_max_depth` in Front Matter.
  max_depth: 6

sidebar:
  # Sidebar Position, available values: left | right (only for Pisces | Gemini).
  position: left
  #position: right

  # Manual define the sidebar width. If commented, will be default for:
  # Muse | Mist: 320
  # Pisces | Gemini: 240
  #width: 300

  # Sidebar Display, available values (only for Muse | Mist):
  #  - post    expand on posts automatically. Default.
  #  - always  expand for all pages automatically.
  #  - hide    expand only when click on the sidebar toggle icon.
  #  - remove  totally remove sidebar including sidebar toggle.
  display: post

  # Sidebar offset from top menubar in pixels (only for Pisces | Gemini).
  offset: 12
  # Enable sidebar on narrow view (only for Muse | Mist).
  onmobile: false
  # Click any blank part of the page to close sidebar (only for Muse | Mist).
  dimmer: false

back2top:
  enable: true
  # Back to top in sidebar.
  sidebar: true
  # Scroll percent label in b2t button.
  scrollpercent: true

# A button to open designated chat widget in sidebar.
# Firstly, you need enable the chat service you want to activate its sidebar button.
chat:
  enable: false
  #service: chatra
  #service: tidio
  icon: comment # icon in Font Awesome 4, set false to disable icon
  text: Chat # button text, change it as you wish


# ---------------------------------------------------------------
# Post Settings
# See: https://theme-next.org/docs/theme-settings/posts
# ---------------------------------------------------------------

# Set the text alignment in the posts.
text_align:
  # Available values: start | end | left | right | center | justify | justify-all | match-parent
  desktop: justify
  mobile: justify

# Automatically scroll page to section which is under <!-- more --> mark.
scroll_to_more: true

# Automatically saving scroll position on each post / page in cookies.
save_scroll: false

# Automatically excerpt description in homepage as preamble text.
excerpt_description: true

# Automatically Excerpt (Not recommend).
# Use <!-- more --> in the post to control excerpt accurately.
auto_excerpt:
  enable: false
  length: 150

# Read more button
# If true, the read more button would be displayed in excerpt section.
read_more_btn: true

# Post meta display settings
post_meta:
  item_text: true
  created_at: true
  updated_at:
    enable: true
    another_day: true
  categories: true

# Post wordcount display settings
# Dependencies: https://github.com/theme-next/hexo-symbols-count-time
symbols_count_time:
  separated_meta: true
  item_text_post: true
  item_text_total: false
  awl: 4
  wpm: 275

codeblock:
  # Manual define the border radius in codeblock, leave it blank for the default value: 1
  border_radius:
  # Add copy button on codeblock
  copy_button:
    enable: false
    # Show text copy result
    show_result: false
    # Style: only 'flat' is currently available, leave it blank if you prefer default theme
    style:

# Wechat Subscriber
wechat_subscriber:
  enable: false
  #qcode: /path/to/your/wechatqcode e.g. /uploads/wechat-qcode.jpg
  #description: e.g. subscribe to my blog by scanning my public wechat account

# Reward (Donate)
reward_settings:
  # If true, reward would be displayed in every article by default.
  # You can show or hide reward in a specific article throuth `reward: true | false` in Front Matter.
  enable: false
  animation: false
  #comment: Donate comment here

reward:
  #wechatpay: /images/wechatpay.png
  #alipay: /images/alipay.png
  #bitcoin: /images/bitcoin.png

# Related popular posts
# Dependencies: https://github.com/tea3/hexo-related-popular-posts
related_posts:
  enable: false
  title: # custom header, leave empty to use the default one
  display_in_home: false
  params:
    maxCount: 5
    #PPMixingRate: 0.0
    #isDate: false
    #isImage: false
    #isExcerpt: false

# Post edit
# Dependencies: https://github.com/hexojs/hexo-deployer-git
post_edit:
  enable: false
  url: https://github.com/user-name/repo-name/tree/branch-name/subdirectory-name # Link for view source.
  #url: https://github.com/user-name/repo-name/edit/branch-name/subdirectory-name # Link for fork & edit.


# ---------------------------------------------------------------
# Misc Theme Settings
# ---------------------------------------------------------------

# Reduce padding / margin indents on devices with narrow width.
mobile_layout_economy: false

# Android Chrome header panel color ($brand-bg / $headband-bg => $black-deep).
android_chrome_color: "#f06632"

# Hide sticky headers and color the menu bar on Safari (iOS / macOS).
safari_rainbow: false

# Optimize the display of scrollbars on webkit based browsers.
custom_scrollbar: false

# Custom Logo
# Do not support Scheme Mist currently.
custom_logo:
  enable: false
  image: #/uploads/custom-logo.jpg

# Code Highlight theme
# Available values: normal | night | night eighties | night blue | night bright
# https://github.com/chriskempson/tomorrow-theme
highlight_theme: normal

# Enable "cheers" for archive page.
cheers: true

# TagCloud settings for tags page.
tagcloud:
  # If true, font size, font color and amount of tags can be customized
  enable: false
  # All values below are same as default, change them by yourself
  min: 12 # min font size in px
  max: 30 # max font size in px
  start: "#ccc" # start color (hex, rgba, hsla or color keywords)
  end: "#111" # end color (hex, rgba, hsla or color keywords)
  amount: 200 # amount of tags, chage it if you have more than 200 tags


# ---------------------------------------------------------------
# Font Settings. Introduced in NexT v5.0.1.
# Find fonts on Google Fonts (https://www.google.com/fonts)
# All fonts set here will have the following styles:
#   light, light italic, normal, normal italic, bold, bold italic
# Be aware that setting too much fonts will cause site running slowly
# ---------------------------------------------------------------
# To avoid space between header and sidebar in scheme Pisces / Gemini, Web Safe fonts are recommended for `global` (and `logo`):
# Arial | Tahoma | Helvetica | Times New Roman | Courier New | Verdana | Georgia | Palatino | Garamond | Comic Sans MS | Trebuchet MS
# ---------------------------------------------------------------

font:
  enable: false

  # Uri of fonts host, e.g. //fonts.googleapis.com (Default).
  host:

  # Font options:
  # `external: true` will load this font family from `host` above.
  # `family: Times New Roman`. Without any quotes.
  # `size: xx`. Use `px` as unit.

  # Global font settings used for all elements in <body>.
  global:
    external: true
    family: Lato
    size:

  # Font settings for Headlines (H1, H2, H3, H4, H5, H6).
  # Fallback to `global` font settings.
  headings:
    external: true
    family:
    size:

  # Font settings for posts.
  # Fallback to `global` font settings.
  posts:
    external: true
    family:

  # Font settings for Logo.
  # Fallback to `global` font settings.
  logo:
    external: true
    family:
    size:

  # Font settings for <code> and code blocks.
  codes:
    external: true
    family:
    size:


# ---------------------------------------------------------------
# Third Party Services Settings
# See: https://theme-next.org/docs/third-party-services/
# You may need to install dependencies or set CDN URLs in `vendors`
# There are two different CDN providers by default:
#   - jsDelivr (cdn.jsdelivr.net), works everywhere even in China
#   - CDNJS (cdnjs.cloudflare.com), provided by cloudflare
# ---------------------------------------------------------------

# Math Equations Render Support
math:
  enable: false

  # Default (true) will load mathjax / katex script on demand.
  # That is it only render those page which has `mathjax: true` in Front Matter.
  # If you set it to false, it will load mathjax / katex srcipt EVERY PAGE.
  per_page: true

  engine: mathjax
  #engine: katex

  # hexo-rendering-pandoc (or hexo-renderer-kramed) needed to full MathJax support.
  mathjax:
    cdn: //cdn.jsdelivr.net/npm/mathjax@2/MathJax.js?config=TeX-AMS-MML_HTMLorMML
    #cdn: //cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.5/MathJax.js?config=TeX-MML-AM_CHTML

    # See: https://mhchem.github.io/MathJax-mhchem/
    #mhchem: //cdn.jsdelivr.net/npm/mathjax-mhchem@3
    #mhchem: //cdnjs.cloudflare.com/ajax/libs/mathjax-mhchem/3.3.0

  # hexo-renderer-markdown-it-plus (or hexo-renderer-markdown-it with markdown-it-katex plugin) needed to full Katex support.
  katex:
    cdn: //cdn.jsdelivr.net/npm/katex@0/dist/katex.min.css
    #cdn: //cdnjs.cloudflare.com/ajax/libs/KaTeX/0.7.1/katex.min.css

    copy_tex:
      # See: https://github.com/KaTeX/KaTeX/tree/master/contrib/copy-tex
      enable: false
      copy_tex_js: //cdn.jsdelivr.net/npm/katex@0/dist/contrib/copy-tex.min.js
      copy_tex_css: //cdn.jsdelivr.net/npm/katex@0/dist/contrib/copy-tex.min.css

# Han Support
# Dependencies: https://github.com/theme-next/theme-next-han
han: false

# Pangu Support
# Dependencies: https://github.com/theme-next/theme-next-pangu
# For more information: https://github.com/vinta/pangu.js
pangu: false

# Quicklink Support
# Dependencies: https://github.com/theme-next/theme-next-quicklink
# Visit https://github.com/GoogleChromeLabs/quicklink for details
quicklink:
  enable: false

  # Quicklink (quicklink.umd.js script) is loaded on demand
  # Add `quicklink: true` in Front Matter of the page or post you need
  # Home page and archive page can be controlled through home and archive options below
  home: true
  archive: true

  # Default (true) will initialize quicklink after the load event fires
  delay: true
  # Custom a time in milliseconds by which the browser must execute prefetching
  timeout: 3000
  # Default (true) will enable fetch() or falls back to XHR
  priority: true

  # For more flexibility you can add some patterns (RegExp, Function, or Array) to ignores
  # See: https://github.com/GoogleChromeLabs/quicklink#custom-ignore-patterns
  # Leave ignores as empty if you don't understand what it means
  # Example:
  # ignores:
  #   - /\/api\/?/
  #   - uri => uri.includes('.xml')
  #   - (uri, el) => el.hasAttribute('noopener')
  ignores:

# Bookmark Support
# Dependencies: https://github.com/theme-next/theme-next-bookmark
bookmark:
  enable: false
  # If auto, save the reading position when closing the page or clicking the bookmark-icon.
  # If manual, only save it by clicking the bookmark-icon.
  save: auto

# Reading progress bar
# Dependencies: https://github.com/theme-next/theme-next-reading-progress
reading_progress:
  enable: false
  color: "#37c6c0"
  height: 2px

# Google Calendar
# Share your recent schedule to others via calendar page.
# API Documentation: https://developers.google.com/google-apps/calendar/v3/reference/events/list
# To get api_key: https://console.developers.google.com
# Create & manage a public Google calendar: https://support.google.com/calendar/answer/37083
calendar:
  enable: false
  calendar_id: <required> # Your Google account E-Mail
  api_key: <required>
  orderBy: startTime
  offsetMax: 24 # Time Range
  offsetMin: 4 # Time Range
  showDeleted: false
  singleEvents: true
  maxResults: 250


# ---------------------------------------------------------------
# Comments and Widgets
# See: https://theme-next.org/docs/third-party-services/comments-and-widgets
# ---------------------------------------------------------------

# Disqus
disqus:
  enable: false
  shortname:
  count: true
  lazyload: false

# DisqusJS
# Alternative Disqus - Render comment component using Disqus API
# Demo: https://suka.js.org/DisqusJS/
disqusjs:
  enable: false
  # API Endpoint of Disqus API (https://disqus.com/api/)
  # leave api empty if you are able to connect to Disqus API
  # otherwise you need a reverse proxy for Disqus API
  # For example:
  # api: https://disqus.skk.moe/disqus/
  api:
  apikey: # register new application from https://disqus.com/api/applications/
  shortname: # See: https://disqus.com/admin/settings/general/

# Changyan
changyan:
  enable: false
  appid:
  appkey:

# Valine
# You can get your appid and appkey from https://leancloud.cn
# More info available at https://valine.js.org
valine:
  enable: false # When enable is set to be true, leancloud_visitors is recommended to be closed for the re-initialization problem within different leancloud adk version.
  appid:  # your leancloud application appid
  appkey:  # your leancloud application appkey
  notify: false # mail notifier, See: https://github.com/xCss/Valine/wiki
  verify: false # Verification code
  placeholder: Just go go # comment box placeholder
  avatar: mm # gravatar style
  guest_info: nick,mail,link # custom comment header
  pageSize: 10 # pagination size
  language: # language, available values: en, zh-cn
  visitor: false # leancloud-counter-security is not supported for now. When visitor is set to be true, appid and appkey are recommended to be the same as leancloud_visitors' for counter compatibility. Article reading statistic https://valine.js.org/visitor.html
  comment_count: true # if false, comment count will only be displayed in post page, not in home page

# LiveRe comments system
# You can get your uid from https://livere.com/insight/myCode (General web site)
#livere_uid: your uid

# Gitment
# Introduction: https://github.com/imsun/gitment
gitment:
  enable: false
  mint: true # RECOMMEND, A mint on Gitment, to support count, language and proxy_gateway
  count: true # Show comments count in post meta area
  lazy: false # Comments lazy loading with a button
  cleanly: false # Hide 'Powered by ...' on footer, and more
  language: # Force language, or auto switch by theme
  github_user: # MUST HAVE, Your Github Username
  github_repo: # MUST HAVE, The name of the repo you use to store Gitment comments
  client_id: # MUST HAVE, Github client id for the Gitment
  client_secret: # EITHER this or proxy_gateway, Github access secret token for the Gitment
  proxy_gateway: # Address of api proxy, See: https://github.com/aimingoo/intersect
  redirect_protocol: # Protocol of redirect_uri with force_redirect_protocol when mint enabled

# Gitalk
# Demo: https://gitalk.github.io
gitalk:
  enable: false
  github_id:  # Github repo owner
  repo:  # Repository name to store issues
  client_id:  # Github Application Client ID
  client_secret:  # Github Application Client Secret
  admin_user:  # GitHub repo owner and collaborators, only these guys can initialize github issues
  distraction_free_mode: true # Facebook-like distraction free mode
  # Gitalk's display language depends on user's browser or system environment
  # If you want everyone visiting your site to see a uniform language, you can set a force language value
  # Available values: en, es-ES, fr, ru, zh-CN, zh-TW
  language:


# ---------------------------------------------------------------
# Content Sharing Services
# See: https://theme-next.org/docs/third-party-services/content-sharing-services
# ---------------------------------------------------------------

# Baidu Share
# Available values: button | slide
# Warning: Baidu Share does not support https.
#baidushare:
##  type: button

# AddThis Share, See: https://www.addthis.com
# Go to https://www.addthis.com/dashboard to customize your tools.
#add_this_id:

# Likely Share
# See: https://ilyabirman.net/projects/likely/
# Likely supports four looks, nine social networks, any button text
# You are free to modify the text value and order of any network
likely:
  enable: false
  look: normal # available values: normal, light, small, big
  networks:
    twitter: Tweet
    facebook: Share
    linkedin: Link
    gplus: Plus
    vkontakte: Share
    odnoklassniki: Class
    telegram: Send
    whatsapp: Send
    pinterest: Pin

# NeedMoreShare2
# Dependencies: https://github.com/theme-next/theme-next-needmoreshare2
# iconStyle: default | box
# boxForm: horizontal | vertical
# position: top / middle / bottom + Left / Center / Right
# networks:
# Weibo,Wechat,Douban,QQZone,Twitter,Facebook,Linkedin,Mailto,Reddit,Delicious,StumbleUpon,Pinterest,
# GooglePlus,Tumblr,GoogleBookmarks,Newsvine,Evernote,Friendfeed,Vkontakte,Odnoklassniki,Mailru
needmoreshare2:
  enable: false
  postbottom:
    enable: false
    options:
      iconStyle: box
      boxForm: horizontal
      position: bottomCenter
      networks: Weibo,Wechat,Douban,QQZone,Twitter,Facebook
  float:
    enable: false
    options:
      iconStyle: box
      boxForm: horizontal
      position: middleRight
      networks: Weibo,Wechat,Douban,QQZone,Twitter,Facebook


# ---------------------------------------------------------------
# Statistics and Analytics
# See: https://theme-next.org/docs/third-party-services/statistics-and-analytics
# ---------------------------------------------------------------

# Baidu Analytics ID
#baidu_analytics:

# Growingio Analytics ID
# Copyright 2015-2018 GrowingIO, Inc. More info available at https://www.growingio.com
#growingio_analytics: #your projectId

# Google Analytics
#google_analytics:
#  tracking_id:
#  localhost_ignored: true

# CNZZ count
cnzz_siteid: 1278600916

# Application Insights
# See: https://azure.microsoft.com/en-us/services/application-insights
#application_insights:

# Post widgets & FB/VK comments settings.
# ---------------------------------------------------------------
# Facebook SDK Support
facebook_sdk:
  enable:       false
  app_id:       #<app_id>
  fb_admin:     #<user_id>
  like_button:  #true
  webmaster:    #true

# Facebook comments plugin
# This plugin depends on Facebook SDK.
# If facebook_sdk.enable is false, Facebook comments plugin is unavailable.
facebook_comments_plugin:
  enable:       false
  num_of_posts: 10    # min posts num is 1
  width:        100%  # default width is 550px
  scheme:       light # default scheme is light (light or dark)

# VKontakte API Support
# To get your AppID visit https://vk.com/editapp?act=create
vkontakte_api:
  enable:       false
  app_id:       #<app_id>
  like:         true
  comments:     true
  num_of_posts: 10

# Star rating support to each article.
# To get your ID visit https://widgetpack.com
rating:
  enable: false
  id:     #<app_id>
  color:  fc6423
# ---------------------------------------------------------------

# Show number of visitors to each article.
# You can visit https://leancloud.cn to get AppID and AppKey.
leancloud_visitors:
  enable: false
  app_id: #<app_id>
  app_key: #<app_key>
  # Dependencies: https://github.com/theme-next/hexo-leancloud-counter-security
  # If you don't care about security in leancloud counter and just want to use it directly
  # (without hexo-leancloud-counter-security plugin), set `security` to `false`.
  security: true
  betterPerformance: false

# Another tool to show number of visitors to each article.
# Visit https://console.firebase.google.com/u/0/ to get apiKey and projectId.
# Visit https://firebase.google.com/docs/firestore/ to get more information about firestore.
firestore:
  enable: false
  collection: articles #required, a string collection name to access firestore database
  apiKey: #required
  projectId: #required
  bluebird: false #enable this if you want to include bluebird 3.5.1(core version) Promise polyfill

# Show Views / Visitors of the website / page with busuanzi.
# Get more information on http://ibruce.info/2015/04/04/busuanzi
busuanzi_count:
  enable: false
  total_visitors: true
  total_visitors_icon: user
  total_views: true
  total_views_icon: eye
  post_views: true
  post_views_icon: eye

# Tencent analytics ID
#tencent_analytics:

# Tencent MTA ID
#tencent_mta:


# ---------------------------------------------------------------
# Search Services
# See: https://theme-next.org/docs/third-party-services/search-services
# ---------------------------------------------------------------

# Algolia Search
# See: https://theme-next.org/docs/third-party-services/search-services#Algolia-Search
# Dependencies: https://github.com/theme-next/theme-next-algolia-instant-search
algolia_search:
  enable: false
  hits:
    per_page: 10
  labels:
    input_placeholder: Search for Posts
    hits_empty: "We didn't find any results for the search: ${query}"
    hits_stats: "${hits} results found in ${time} ms"

# Local search
# Dependencies: https://github.com/theme-next/hexo-generator-searchdb
local_search:
  enable: true
  # If auto, trigger search by changing input.
  # If manual, trigger search by pressing enter key or search button.
  trigger: auto
  # Show top n results per article, show all results by setting to -1
  top_n_per_article: -1
  # Unescape html strings to the readable one.
  unescape: false

# Swiftype Search API Key
#swiftype_key:


# ---------------------------------------------------------------
# Chat Services
# See: https://theme-next.org/docs/third-party-services/chat-services
# ---------------------------------------------------------------

# Chatra Support
# See: https://chatra.io
# Dashboard: https://app.chatra.io/settings/general
chatra:
  enable: false
  async: true
  id: # visit Dashboard to get your ChatraID
  #embed: # unfinished experimental feature for developers, See: https://chatra.io/help/api/#injectto

# Tidio Support
# See: https://www.tidiochat.com
# Dashboard: https://www.tidiochat.com/panel/dashboard
tidio:
  enable: false
  key: # Public Key, get it from Dashboard, See: https://www.tidiochat.com/panel/settings/developer


# ---------------------------------------------------------------
# Tags Settings
# See: https://theme-next.org/docs/tag-plugins/
# ---------------------------------------------------------------

# Note tag (bs-callout)
note:
  # Note tag style values:
  #  - simple    bs-callout old alert style. Default.
  #  - modern    bs-callout new (v2-v3) alert style.
  #  - flat      flat callout style with background, like on Mozilla or StackOverflow.
  #  - disabled  disable all CSS styles import of note tag.
  style: simple
  icons: true
  border_radius: 3
  # Offset lighter of background in % for modern and flat styles (modern: -12 | 12; flat: -18 | 6).
  # Offset also applied to label tag variables. This option can work with disabled note tag.
  light_bg_offset: 0

# Tabs tag
tabs:
  enable: true
  transition:
    tabs: false
    labels: true
  border_radius: 0

# PDF tag, requires two plugins: pdfObject and pdf.js
# pdfObject will try to load pdf files natively, if failed, pdf.js will be used.
# The following `cdn` setting is only for pdfObject, because cdn for pdf.js might be blocked by CORS policy.
# So, you must install the dependency of pdf.js if you want to use pdf tag and make it available to all browsers.
# See: https://github.com/theme-next/theme-next-pdf
pdf:
  enable: false
  # Default height
  height: 500px
  pdfobject:
    cdn: //cdn.jsdelivr.net/npm/pdfobject@2/pdfobject.min.js
    #cdn: //cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js

# Mermaid tag
mermaid:
  enable: false
  # Available themes: default | dark | forest | neutral
  theme: forest
  cdn: //cdn.jsdelivr.net/npm/mermaid@8/dist/mermaid.min.js
  #cdn: //cdnjs.cloudflare.com/ajax/libs/mermaid/8.0.0/mermaid.min.js


# ---------------------------------------------------------------
# Animation Settings
# ---------------------------------------------------------------

# Use velocity to animate everything.
motion:
  enable: false
  async: false
  transition:
    # Transition variants:
    # fadeIn | fadeOut | flipXIn | flipXOut | flipYIn | flipYOut | flipBounceXIn | flipBounceXOut | flipBounceYIn | flipBounceYOut
    # swoopIn | swoopOut | whirlIn | whirlOut | shrinkIn | shrinkOut | expandIn | expandOut
    # bounceIn | bounceOut | bounceUpIn | bounceUpOut | bounceDownIn | bounceDownOut | bounceLeftIn | bounceLeftOut | bounceRightIn | bounceRightOut
    # slideUpIn | slideUpOut | slideDownIn | slideDownOut | slideLeftIn | slideLeftOut | slideRightIn | slideRightOut
    # slideUpBigIn | slideUpBigOut | slideDownBigIn | slideDownBigOut | slideLeftBigIn | slideLeftBigOut | slideRightBigIn | slideRightBigOut
    # perspectiveUpIn | perspectiveUpOut | perspectiveDownIn | perspectiveDownOut | perspectiveLeftIn | perspectiveLeftOut | perspectiveRightIn | perspectiveRightOut
    post_block: fadeIn
    post_header: slideDownIn
    post_body: slideDownIn
    coll_header: slideLeftIn
    # Only for Pisces | Gemini.
    sidebar: slideUpIn

# Fancybox. There is support for old version 2 and new version 3.
# Choose only one variant, do not need to install both.
# To install 2.x: https://github.com/theme-next/theme-next-fancybox
# To install 3.x: https://github.com/theme-next/theme-next-fancybox3
fancybox: false

# Polyfill to remove click delays on browsers with touch UIs.
# Dependencies: https://github.com/theme-next/theme-next-fastclick
fastclick: false

# Vanilla JavaScript plugin for lazyloading images.
# Dependencies: https://github.com/theme-next/theme-next-jquery-lazyload
lazyload: false

# Progress bar in the top during page loading.
# Dependencies: https://github.com/theme-next/theme-next-pace
pace: false
# Themes list:
# pace-theme-big-counter | pace-theme-bounce | pace-theme-barber-shop | pace-theme-center-atom
# pace-theme-center-circle | pace-theme-center-radar | pace-theme-center-simple | pace-theme-corner-indicator
# pace-theme-fill-left | pace-theme-flash | pace-theme-loading-bar | pace-theme-mac-osx | pace-theme-minimal
pace_theme: pace-theme-minimal

# Canvas-nest
# Dependencies: https://github.com/theme-next/theme-next-canvas-nest
canvas_nest:
  enable: false
  onmobile: true # display on mobile or not
  color: "0,0,255" # RGB values, use ',' to separate
  opacity: 0.5 # the opacity of line: 0~1
  zIndex: -1 # z-index property of the background
  count: 99 # the number of lines

# JavaScript 3D library.
# Dependencies: https://github.com/theme-next/theme-next-three
# three_waves
three_waves: false
# canvas_lines
canvas_lines: false
# canvas_sphere
canvas_sphere: false

# Canvas-ribbon
# Dependencies: https://github.com/theme-next/theme-next-canvas-ribbon
# size: The width of the ribbon.
# alpha: The transparency of the ribbon.
# zIndex: The display level of the ribbon.
canvas_ribbon:
  enable: false
  size: 300
  alpha: 0.6
  zIndex: -1


#! ---------------------------------------------------------------
#! DO NOT EDIT THE FOLLOWING SETTINGS
#! UNLESS YOU KNOW WHAT YOU ARE DOING
#! See: https://theme-next.org/docs/advanced-settings
#! ---------------------------------------------------------------

# Script Vendors. Set a CDN address for the vendor you want to customize.
# For example
#   jquery: https://ajax.googleapis.com/ajax/libs/jquery/2.2.0/jquery.min.js
# Be aware that you would better use the same version as internal ones to avoid potential problems.
# Please use the https protocol of CDN files when you enable https on your site.
vendors:
  # Internal path prefix. Please do not edit it.
  _internal: lib

  # Internal version: 2.1.3
  # Example:
  # jquery: //cdn.jsdelivr.net/npm/jquery@2/dist/jquery.min.js
  # jquery: //cdnjs.cloudflare.com/ajax/libs/jquery/2.1.3/jquery.min.js
  jquery:

  # Internal version: 2.1.5 & 3.5.7
  # See: https://fancyapps.com/fancybox
  # Example:
  # fancybox: //cdn.jsdelivr.net/gh/fancyapps/fancybox@3/dist/jquery.fancybox.min.js
  # fancybox: //cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.6/jquery.fancybox.min.js
  # fancybox_css: //cdn.jsdelivr.net/gh/fancyapps/fancybox@3/dist/jquery.fancybox.min.css
  # fancybox_css: //cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.6/jquery.fancybox.min.css
  fancybox:
  fancybox_css:

  # Internal version: 1.0.6
  # See: https://github.com/ftlabs/fastclick
  # Example:
  # fastclick: //cdn.jsdelivr.net/npm/fastclick@1/lib/fastclick.min.js
  # fastclick: //cdnjs.cloudflare.com/ajax/libs/fastclick/1.0.6/fastclick.min.js
  fastclick:

  # Internal version: 1.9.7
  # See: https://github.com/tuupola/jquery_lazyload
  # Example:
  # lazyload: //cdn.jsdelivr.net/npm/jquery-lazyload@1/jquery.lazyload.min.js
  # lazyload: //cdnjs.cloudflare.com/ajax/libs/jquery_lazyload/1.9.7/jquery.lazyload.min.js
  lazyload:

  # Internal version: 1.2.1
  # See: http://velocityjs.org
  # Example:
  # velocity: //cdn.jsdelivr.net/npm/velocity-animate@1/velocity.min.js
  # velocity: //cdnjs.cloudflare.com/ajax/libs/velocity/1.2.1/velocity.min.js
  # velocity_ui: //cdn.jsdelivr.net/npm/velocity-animate@1/velocity.ui.min.js
  # velocity_ui: //cdnjs.cloudflare.com/ajax/libs/velocity/1.2.1/velocity.ui.min.js
  velocity:
  velocity_ui:

  # Internal version: 4.6.2
  # See: https://fontawesome.com
  # Example:
  # fontawesome: //cdn.jsdelivr.net/npm/font-awesome@4/css/font-awesome.min.css
  # fontawesome: //cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.2/css/font-awesome.min.css
  fontawesome:

  # Internal version: 2.10.4
  # See: https://www.algolia.com
  # Example:
  # algolia_instant_js: //cdn.jsdelivr.net/npm/instantsearch.js@2/dist/instantsearch.js
  # algolia_instant_css: //cdn.jsdelivr.net/npm/instantsearch.js@2/dist/instantsearch.min.css
  algolia_instant_js:
  algolia_instant_css:

  # Internal version: 1.0.2
  # See: https://github.com/HubSpot/pace
  # Example:
  # pace: //cdn.jsdelivr.net/npm/pace-js@1/pace.min.js
  # pace: //cdnjs.cloudflare.com/ajax/libs/pace/1.0.2/pace.min.js
  # pace_css: //cdn.jsdelivr.net/npm/pace-js@1/themes/blue/pace-theme-minimal.css
  # pace_css: //cdnjs.cloudflare.com/ajax/libs/pace/1.0.2/themes/blue/pace-theme-minimal.min.css
  pace:
  pace_css:

  # Internal version: 1.0.0
  # See: https://github.com/theme-next/theme-next-canvas-nest
  # Example:
  # canvas_nest: //cdn.jsdelivr.net/gh/theme-next/theme-next-canvas-nest@1/canvas-nest.min.js
  # canvas_nest_nomobile: //cdn.jsdelivr.net/gh/theme-next/theme-next-canvas-nest@1/canvas-nest-nomobile.min.js
  canvas_nest:
  canvas_nest_nomobile:

  # Internal version: 1.0.0
  # See: https://github.com/theme-next/theme-next-three
  # Example:
  # three: //cdn.jsdelivr.net/gh/theme-next/theme-next-three@1/three.min.js
  # three_waves: //cdn.jsdelivr.net/gh/theme-next/theme-next-three@1/three-waves.min.js
  # canvas_lines: //cdn.jsdelivr.net/gh/theme-next/theme-next-three@1/canvas_lines.min.js
  # canvas_sphere: //cdn.jsdelivr.net/gh/theme-next/theme-next-three@1/canvas_sphere.min.js
  three:
  three_waves:
  canvas_lines:
  canvas_sphere:

  # Internal version: 1.0.0
  # See: https://github.com/zproo/canvas-ribbon
  # Example:
  # canvas_ribbon: //cdn.jsdelivr.net/gh/theme-next/theme-next-canvas-ribbon@1/canvas-ribbon.js
  canvas_ribbon:

  # Internal version: 3.3.0
  # See: https://github.com/ethantw/Han
  # Example:
  # han: //cdn.jsdelivr.net/npm/han-css@3/dist/han.min.css
  # han: //cdnjs.cloudflare.com/ajax/libs/Han/3.3.0/han.min.css
  han:

  # Internal version: 4.0.7
  # See: https://github.com/vinta/pangu.js
  # Example:
  # pangu: //cdn.jsdelivr.net/npm/pangu@4/dist/browser/pangu.min.js
  # pangu: //cdnjs.cloudflare.com/ajax/libs/pangu/4.0.7/pangu.min.js
  pangu:

  # Internal version: 1.0.0
  # See: https://github.com/GoogleChromeLabs/quicklink
  # Example:
  # quicklink: //cdn.jsdelivr.net/npm/quicklink@1/dist/quicklink.umd.js
  quicklink:

  # Internal version: 1.0.0
  # See: https://github.com/revir/need-more-share2
  # Example:
  # needmoreshare2_js: //cdn.jsdelivr.net/gh/theme-next/theme-next-needmoreshare2@1/needsharebutton.min.js
  # needmoreshare2_css: //cdn.jsdelivr.net/gh/theme-next/theme-next-needmoreshare2@1/needsharebutton.min.css
  needmoreshare2_js:
  needmoreshare2_css:

  # Internal version: 1.0.0
  # See: https://github.com/theme-next/theme-next-bookmark
  # Example:
  # bookmark: //cdn.jsdelivr.net/gh/theme-next/theme-next-bookmark@1/bookmark.min.js
  bookmark:

  # Internal version: 1.1
  # See: https://github.com/theme-next/theme-next-reading-progress
  # Example:
  # reading_progress: //cdn.jsdelivr.net/gh/theme-next/theme-next-reading-progress@1/reading_progress.min.js
  reading_progress:

  # leancloud-storage
  # See: https://www.npmjs.com/package/leancloud-storage
  # Example:
  # leancloud: //cdn.jsdelivr.net/npm/leancloud-storage@3/dist/av-min.js
  leancloud:

  # valine
  # See: https://github.com/xCss/Valine
  # Example:
  # valine: //cdn.jsdelivr.net/npm/valine@1/dist/Valine.min.js
  # valine: //cdnjs.cloudflare.com/ajax/libs/valine/1.3.4/Valine.min.js
  valine:

  # gitalk & js-md5
  # See: https://github.com/gitalk/gitalk, https://github.com/emn178/js-md5
  # Example:
  # gitalk_js: //cdn.jsdelivr.net/npm/gitalk@1/dist/gitalk.min.js
  # gitalk_css: //cdn.jsdelivr.net/npm/gitalk@1/dist/gitalk.css
  # md5: //cdn.jsdelivr.net/npm/js-md5@0/src/md5.min.js
  gitalk_js:
  gitalk_css:
  md5:

  # likely
  # See: https://github.com/ilyabirman/Likely
  # Example:
  # likely_js: //cdn.jsdelivr.net/npm/ilyabirman-likely@2/release/likely.js
  # likely_css: //cdn.jsdelivr.net/npm/ilyabirman-likely@2/release/likely.css
  likely_js:
  likely_css:

  # DisqusJS
  # See: https://github.com/SukkaW/DisqusJS
  # Example:
  # disqusjs_js: //cdn.jsdelivr.net/npm/disqusjs@1/dist/disqus.js
  # disqusjs_css: //cdn.jsdelivr.net/npm/disqusjs@1/dist/disqusjs.css
  disqusjs_js:
  disqusjs_css:

# Assets
css: css
js: js
images: images
