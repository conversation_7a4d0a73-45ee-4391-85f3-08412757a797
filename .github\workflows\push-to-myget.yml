name: Push to myget

on: workflow_dispatch

jobs:
  push-to-myget:
    runs-on: windows-latest
    steps:
      - name: checkout
        uses: actions/checkout@master

      - name: Setup .NET Core SDK
        uses: actions/setup-dotnet@master
        with:
          dotnet-version: '8.0.x'
          include-prerelease: true

      - name: setup nuget.exe
        uses: NuGet/setup-nuget@v1.0.5

      - name: build and push
        shell: pwsh
        run: |
          cd build
          .\build.ps1
          nuget pack build-for-myget.nuspec -version 3.5.0-beta.${env:GITHUB_RUN_NUMBER}
          $nupkgName = @(gci *.nupkg)[0].Name
          $mygetSourcePath = "https://www.myget.org/F/handycontrol"
          nuget setApiKey ${{ secrets.MYGETTOKEN }} -Source $mygetSourcePath
          nuget push $nupkgName -Source $mygetSourcePath
