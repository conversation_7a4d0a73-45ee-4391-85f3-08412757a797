::selection {
  background: $selection-bg;
  color: $selection-color;
}

body {
  position: relative; // Required by scrollspy
  font-family: $font-family-base;
  font-size: $font-size-base;
  line-height: $line-height-base;
  color: $text-color;
  background: $body-bg-color;

  +tablet-mobile() { padding-right: 0 !important; }
  +desktop-large() { font-size: $font-size-large; }
}

h1, h2, h3, h4, h5, h6 {
  margin: 20px 0 15px;
  padding: 0;
  font-weight: bold;
  line-height: 1.5;
  font-family: $font-family-headings;
}

for headline in (1..6) {
  h{headline} {
    font-size: $font-size-headings-base - $font-size-headings-step * headline;
    code {
      font-size: 1em;
    }
  }

  +mobile() {
    h{headline} {
      font-size: $font-size-headings-base - $font-size-headings-step * headline - 4px;
      code {
        font-size: 1em;
      }
    }
  }
}

p { margin: 0 0 20px 0; }

a, span.exturl {
  word-wrap();
  // Remove the gray background color from active links in IE 10.
  background-color: transparent;
  color: $link-color;
  text-decoration: none;
  outline: none;
  border-bottom: 1px solid $link-decoration-color;

  &:hover {
    color: $link-hover-color;
    border-bottom-color: $link-decoration-hover-color;
  }

  // For spanned external links.
  cursor: pointer;
}

video {
  max-width: 100%;
  show();
  margin-left: auto;
  margin-right: auto;
}

img {
  show();
  margin: auto;
  max-width: 100%;
  height: auto;
}

hr {
  margin: 40px 0;
  height: 3px;
  border: none;
  background-color: $gray-lighter;
  background-image: repeating-linear-gradient(
    -45deg,
    white,
    white 4px,
    transparent 4px,
    transparent 8px
  );
}

blockquote {
  margin: 0;
  padding: 0 15px;
  color: $grey-dim;
  border-left: 4px solid $gray-lighter;

  cite::before {
    content: "-";
    padding: 0 5px;
  }
}

dt { font-weight: $font-weight-bolder; }

dd {
  margin: 0;
  padding: 0;
}

kbd {
  border: 1px solid $grey-light;
  border-radius: 0.2em;
  box-shadow: 0.1em 0.1em 0.2em rgba(0, 0, 0, 0.1);
  background-color: #f9f9f9;
  font-family: inherit;
  background-image: linear-gradient(top, #eee, white, #eee);
  padding: 0.1em 0.3em;
  white-space: nowrap;
}
