using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;

namespace StandaloneTimePicker.Controls.Clock
{
    /// <summary>
    /// A clock control that displays time selection using lists for hours and minutes
    /// </summary>
    [TemplatePart(Name = ElementHourList, Type = typeof(ListBox))]
    [TemplatePart(Name = ElementMinuteList, Type = typeof(ListBox))]
    public class ListClock : ClockBase
    {
        static ListClock()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(ListClock), new FrameworkPropertyMetadata(typeof(ListClock)));
        }
        #region Constants

        private const string ElementHourList = "PART_HourList";
        private const string ElementMinuteList = "PART_MinuteList";
        private const string ElementButtonNow = "PART_ButtonNow";

        #endregion Constants

        #region Data

        private ListBox _hourList;
        private ListBox _minuteList;
        private Button _buttonNow;

        #endregion Data

        /// <summary>
        /// Called when the clock is opened
        /// </summary>
        public override void OnClockOpened() => ScrollIntoView();

        /// <summary>
        /// Applies the control template
        /// </summary>
        public override void OnApplyTemplate()
        {
            // Cleanup old event handlers
            UnsubscribeEvents();

            base.OnApplyTemplate();

            // Get template parts and setup
            InitializeTemplateParts();

            AppliedTemplate = true;
            if (SelectedTime.HasValue)
            {
                Update(SelectedTime.Value);
            }
        }

        private void UnsubscribeEvents()
        {
            if (ButtonConfirm != null) ButtonConfirm.Click -= ButtonConfirm_OnClick;
            if (_buttonNow != null) _buttonNow.Click -= ButtonNow_OnClick;
            if (_hourList != null) _hourList.SelectionChanged -= OnTimeSelectionChanged;
            if (_minuteList != null) _minuteList.SelectionChanged -= OnTimeSelectionChanged;
        }

        private void InitializeTemplateParts()
        {
            _hourList = GetTemplateChild(ElementHourList) as ListBox;
            if (_hourList != null)
            {
                CreateItemsSource(_hourList, 24);
                _hourList.SelectionChanged += OnTimeSelectionChanged;
            }

            _minuteList = GetTemplateChild(ElementMinuteList) as ListBox;
            if (_minuteList != null)
            {
                CreateItemsSource(_minuteList, 60);
                _minuteList.SelectionChanged += OnTimeSelectionChanged;
            }

            ButtonConfirm = GetTemplateChild(ElementButtonConfirm) as Button;
            if (ButtonConfirm != null) ButtonConfirm.Click += ButtonConfirm_OnClick;

            _buttonNow = GetTemplateChild(ElementButtonNow) as Button;
            if (_buttonNow != null) _buttonNow.Click += ButtonNow_OnClick;
        }

        /// <summary>
        /// Updates the clock with the specified time
        /// </summary>
        /// <param name="time">The time to update to</param>
        internal override void Update(DateTime time)
        {
            if (!AppliedTemplate) return;

            var h = time.Hour;
            var m = time.Minute;

            _hourList.SelectedIndex = h;
            _minuteList.SelectedIndex = m;

            ScrollIntoView();

            DisplayTime = time;
        }

        private void OnTimeSelectionChanged(object sender, SelectionChangedEventArgs e) => UpdateDisplayTime();

        private void ButtonNow_OnClick(object sender, RoutedEventArgs e)
        {
            var now = DateTime.Now;
            _hourList.SelectedIndex = now.Hour;
            _minuteList.SelectedIndex = now.Minute;
            UpdateDisplayTime();
            TriggerConfirmed();
        }

        private void CreateItemsSource(ItemsControl selector, int count)
        {
            var list = new List<string>();
            for (var i = 0; i < count; i++)
            {
                list.Add(i.ToString("00"));
            }

            selector.ItemsSource = list;
        }

        private void UpdateDisplayTime()
        {
            if (_hourList?.SelectedIndex >= 0 && _hourList.SelectedIndex < 24 &&
                _minuteList?.SelectedIndex >= 0 && _minuteList.SelectedIndex < 60)
            {
                var today = DateTime.Today;
                DisplayTime = new DateTime(today.Year, today.Month, today.Day,
                    _hourList.SelectedIndex, _minuteList.SelectedIndex, 0);
            }
        }

        private void ScrollIntoView()
        {
            _hourList.ScrollIntoView(_hourList.SelectedItem);
            _minuteList.ScrollIntoView(_minuteList.SelectedItem);
        }
    }
}
