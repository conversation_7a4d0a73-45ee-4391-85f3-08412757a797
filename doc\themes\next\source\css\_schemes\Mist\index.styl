//
// Mist scheme
// =================================================

@import "_base";
@import "outline/outline";
@import "_header";
@import "_logo";
@import "_menu";
@import "_search.styl";
@import "_posts-expanded";
@import "sidebar/sidebar-blogroll";

// Components
// --------------------------------------------------
.btn {
  padding: 0 10px;
  border-width: 2px;
  border-radius: 0;
}

.headband { display: none; }

// Search
// --------------------------------------------------
.site-search {
  position: relative;
  float: right;
  margin-top: 5px;
  padding-top: 3px;

  +mobile() {
    float: none;
    padding: 0 10px;
  }
}

// Page - Container
// --------------------------------------------------
.container .main-inner {
  +mobile() { width: auto; }
}

// Page - Post details
// --------------------------------------------------
.page-post-detail {
  .post-title,
  .post-meta { text-align: center; }

  .post-title:before { display: none; }

  .post-meta { margin-bottom: 60px; }
}

// Pagination
// --------------------------------------------------
.pagination {
  margin: 120px 0 0;
  text-align: left;

  +mobile() {
    margin: 80px 10px 0;
    text-align: center;
  }
}

// Footer
// --------------------------------------------------
.footer {
  margin-top: 80px;
  padding: 10px 0;
  background: $whitesmoke;
  color: $grey-dim;
}
.footer-inner {
  margin: 0 auto;
  text-align: left;

  +mobile() {
    width: auto;
    text-align: center;
  }
}

// Helpers
// --------------------------------------------------
