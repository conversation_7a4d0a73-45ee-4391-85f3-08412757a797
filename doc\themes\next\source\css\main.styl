// CSS Style Guide: http://codeguide.co/#css


$scheme    = hexo-config('scheme') ? hexo-config('scheme') : 'Muse';

$custom_styles = hexo-config('custom_file_path.styles') ? "../../../../../" + hexo-config('custom_file_path.styles') : custom;
$custom_mixins = hexo-config('custom_file_path.mixins') ? "../../../../../" + hexo-config('custom_file_path.mixins') : custom;
$custom_variables = hexo-config('custom_file_path.variables') ? "../../../../../" + hexo-config('custom_file_path.variables') : custom;

$variables = base $scheme $custom_variables;
$mixins    = base $scheme $custom_mixins;


// Variables Layer
// --------------------------------------------------
for $variable in $variables
  @import "_variables/" + $variable;


// Mixins Layer
// --------------------------------------------------
for $mixin in $mixins
  @import "_mixins/" + $mixin;


// Common Layer
// --------------------------------------------------

// Scaffolding
@import "_common/scaffolding";

// Layout
@import "_common/outline";

// Components
@import "_common/components";


// Schemes Layer
// --------------------------------------------------
@import "_schemes/" + $scheme;


// Custom Layer
// --------------------------------------------------
@import "_custom/" + $custom_styles;
