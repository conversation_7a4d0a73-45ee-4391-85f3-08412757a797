// Blockquote with all children centered.
.blockquote-center {
  position: relative;
  margin: 40px 0;
  padding: 0;
  border-left: none;
  text-align: center;

  &::before, &::after {
    position: absolute;
    content: ' ';
    show();
    width: 100%;
    height: 24px;
    opacity: 0.2;
    background-repeat: no-repeat;
    background-position: 0 -6px;
    background-size: 22px 22px;
  }
  &::before {
    top: -20px;
    background-image: url($center-quote-left);
    border-top: 1px solid $grey-light;
  }
  &::after {
    bottom: -20px;
    background-image: url($center-quote-right);
    border-bottom: 1px solid $grey-light;
    background-position: 100% 8px;
  }

  p, div { text-align: center; }
}
