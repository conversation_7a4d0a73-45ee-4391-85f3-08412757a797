.post-toc-empty {
  font-size: 14px;
  color: $grey-dim;
}

.post-toc-wrap { overflow: hidden; }

.post-toc { overflow: auto; }

.post-toc ol {
  margin: 0;
  padding: 0 2px 5px 10px;
  text-align: left;
  list-style: none;
  font-size: 14px;

  & > ol { padding-left: 0; }

  a {
    the-transition();
    transition-property: all;
    color: $toc-link-color;
    border-bottom-color: $toc-link-border-color;

    &:hover {
      color: $toc-link-hover-color;
      border-bottom-color: $toc-link-hover-border-color;
    }
  }
}

.post-toc .nav-item {
  overflow: hidden;
  text-overflow: ellipsis;
  //text-align: justify;
  white-space: nowrap if !hexo-config('toc.wrap');
  line-height: 1.8;
}

.post-toc .nav .nav-child {
  display: hexo-config('toc.expand_all') ? block : none;
}

.post-toc .nav .active > .nav-child { display: block; }

.post-toc .nav .active-current > .nav-child {
  show();
  & > .nav-item { display: block; }
}

.post-toc .nav .active > a {
  color: $toc-link-active-color;
  border-bottom-color: $toc-link-active-border-color;
}

.post-toc .nav .active-current > a {
  color: $toc-link-active-current-color;
  &:hover {
    color: $toc-link-active-current-border-color;
  }
}
