// https://github.com/chriskempson/tomorrow-theme

@require "theme"
@require "diff"

// Placeholder: $code-block
$code-block {
  overflow: auto;
  margin: 20px 0;
  padding: 0;
  font-size $code-font-size;
  color: $highlight-foreground;
  background: $highlight-background;
  line-height: $line-height-code-block;
}

pre, code { font-family: $code-font-family; }

code {
  word-wrap();
  padding: 2px 4px;
  color: $code-foreground;
  background: $code-background;
  border-radius: $code-border-radius;
  font-size: $code-font-size;
}

pre {
  @extend $code-block;
  padding: 10px;
  code {
    padding: 0;
    color: $highlight-foreground;
    background: none;
    text-shadow: none;
  }
}

.highlight {
  @extend $code-block;
  // Read values from NexT config and set they as local variables to use as string variables (in any CSS section).
  hexo-config('codeblock.border_radius') is a 'unit' ? (cbradius = unit(hexo-config('codeblock.border_radius'), px)) : (cbradius = 1px)
  border-radius: cbradius;

  pre {
    border: none;
    margin: 0;
    padding: 10px 0;
  }

  table {
    margin: 0;
    width: auto;
    border: none;
  }

  td {
    border: none;
    padding: 0;
  }

  figcaption {
    clearfix();
    font-size: 1em;
    color: $highlight-foreground;
    line-height: 1em;
    margin-bottom: 1em;
    margin: 0em;
    padding: 0.5em;
    background: $code-background;
    border-bottom: 1px solid #e9e9e9;

    a {
      float: right;
      color: $highlight-foreground;

      &:hover { border-bottom-color: $highlight-foreground; }
    }
  }

  .gutter pre {
    padding-left: 10px
    padding-right: 10px
    color: $highlight-gutter.color
    text-align: right
    background-color: $highlight-gutter.bg-color
  }

  .code pre {
    width: 100%
    padding-left: 10px
    padding-right: 10px
    background-color: $highlight-background
  }

  .line { height: 20px; }
}

.gutter {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.gist table {
  width: auto;

  td { border: none; }
}

// For diff highlight
pre .deletion { background: $highlight-deletion; }
pre .addition { background: $highlight-addition; }
pre .meta     { color: $highlight-purple; }

pre {

  .comment { color: $highlight-comment; }

  .variable
  .attribute
  .tag
  .name
  .regexp
  .ruby .constant
  .xml .tag .title
  .xml .pi
  .xml .doctype
  .html .doctype
  .css .id
  .css .class
  .css .pseudo {
    color: $highlight-red;
  }

  .number
  .preprocessor
  .built_in
  .builtin-name
  .literal
  .params
  .constant
  .command {
    color: $highlight-orange;
  }

  .ruby .class .title
  .css .rules .attribute
  .string
  .symbol
  .value
  .inheritance
  .header
  .ruby .symbol
  .xml .cdata
  .special
  .formula {
    color: $highlight-green;
  }

  .title
  .css .hexcolor {
    color: $highlight-aqua;
  }

  .function
  .python .decorator
  .python .title
  .ruby .function .title
  .ruby .title .keyword
  .perl .sub
  .javascript .title
  .coffeescript .title {
    color: $highlight-blue;
  }

  .keyword
  .javascript .function {
    color: $highlight-purple;
  }
}
