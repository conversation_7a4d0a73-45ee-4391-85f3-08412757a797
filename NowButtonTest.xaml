<Window x:Class="NowButtonTest.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:StandaloneTimePicker.Controls;assembly=StandaloneTimePicker"
        xmlns:clock="clr-namespace:StandaloneTimePicker.Controls.Clock;assembly=StandaloneTimePicker"
        Title="NOW BUTTON: ListClock Enhancement" Height="550" Width="750">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0" Text="✅ ADDED: 'Now' Button to ListClock" 
                   FontSize="20" FontWeight="Bold" Foreground="Green" Margin="0,0,0,20"/>
        
        <TextBlock Grid.Row="1" Text="TimePicker with ListClock (now has 'Now' button):" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="2" x:Name="ListClockTimePicker" Width="300" HorizontalAlignment="Left" Margin="0,0,0,15">
            <controls:TimePicker.Clock>
                <clock:ListClock/>
            </controls:TimePicker.Clock>
        </controls:TimePicker>
        
        <TextBlock Grid.Row="3" Text="Standard TimePicker with Analog Clock (for comparison):" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="4" x:Name="AnalogTimePicker" Width="300" HorizontalAlignment="Left"/>
        
        <Border Grid.Row="5" BorderBrush="LightGray" BorderThickness="1" Margin="0,20,0,0" Padding="15">
            <StackPanel>
                <TextBlock Text="✅ 'NOW' BUTTON IMPLEMENTATION:" FontWeight="Bold" FontSize="16" Foreground="DarkBlue" Margin="0,0,0,10"/>
                
                <TextBlock Text="New Feature Added:" FontWeight="Bold" Margin="0,0,0,5"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="'Now' button positioned to the left of 'Confirm' button"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Matches existing button styling (ButtonCustom style)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Sets current system time (DateTime.Now) when clicked"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Automatically closes dropdown and updates TimePicker display"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Triggers same events as manual time selection"/>
                </StackPanel>
                
                <TextBlock Text="Implementation Details:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Button Layout: Horizontal StackPanel with 'Now' (50px) and 'Confirm' (60px)" Margin="0,2"/>
                <TextBlock Text="• Button Styling: Uses ButtonCustom style for consistency" Margin="0,2"/>
                <TextBlock Text="• Event Handler: ButtonNow_OnClick sets current time and closes popup" Margin="0,2"/>
                <TextBlock Text="• Time Setting: Uses DateTime.Now for hours and minutes (seconds = 0)" Margin="0,2"/>
                <TextBlock Text="• List Updates: Automatically scrolls to current hour and minute" Margin="0,2"/>
                <TextBlock Text="• Event Triggering: Calls TriggerConfirmed() for proper event handling" Margin="0,2"/>
                
                <TextBlock Text="Button Specifications:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• 'Now' Button: 50px width, left positioned with 5px right margin" Margin="0,2"/>
                <TextBlock Text="• 'Confirm' Button: 60px width, right positioned with 5px left margin" Margin="0,2"/>
                <TextBlock Text="• Colors: PrimaryBrush foreground, transparent background" Margin="0,2"/>
                <TextBlock Text="• Font: Matches existing button styling" Margin="0,2"/>
                
                <TextBlock Text="Functionality:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Quick Time Selection: One-click to set current time" Margin="0,2"/>
                <TextBlock Text="• Automatic Scrolling: Lists scroll to show current hour/minute" Margin="0,2"/>
                <TextBlock Text="• Popup Closure: Automatically closes after selection" Margin="0,2"/>
                <TextBlock Text="• Event Consistency: Fires same events as manual selection" Margin="0,2"/>
                <TextBlock Text="• Time Format: HH:mm format (seconds always 0)" Margin="0,2"/>
                
                <TextBlock Text="User Experience Benefits:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Convenience: Quick way to select current time" Margin="0,2"/>
                <TextBlock Text="• Efficiency: No need to manually scroll through hours/minutes" Margin="0,2"/>
                <TextBlock Text="• Consistency: Matches behavior of manual time selection" Margin="0,2"/>
                <TextBlock Text="• Professional: Clean button layout and styling" Margin="0,2"/>
                
                <TextBlock Text="Test Instructions:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Click the ListClock TimePicker dropdown button" Margin="0,2"/>
                <TextBlock Text="• You should see 'Now' and 'Confirm' buttons at the bottom" Margin="0,2"/>
                <TextBlock Text="• Click the 'Now' button to set current system time" Margin="0,2"/>
                <TextBlock Text="• Verify the dropdown closes and current time appears in text input" Margin="0,2"/>
                <TextBlock Text="• Test that the time matches your system clock (HH:mm format)" Margin="0,2"/>
                
                <TextBlock Text="🎉 'Now' button provides quick current time selection!" 
                           FontWeight="Bold" FontSize="14" Foreground="DarkGreen" Margin="0,15,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
