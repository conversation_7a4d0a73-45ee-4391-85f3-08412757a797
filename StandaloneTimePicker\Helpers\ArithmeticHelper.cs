using System;
using System.Windows;

namespace StandaloneTimePicker.Helpers
{
    /// <summary>
    /// Contains simple arithmetic algorithms used internally
    /// </summary>
    internal class ArithmeticHelper
    {
        /// <summary>
        /// Divides an integer evenly into an array
        /// </summary>
        /// <param name="num">The number to divide</param>
        /// <param name="count">The number of array elements</param>
        /// <returns>An array with the divided values</returns>
        public static int[] DivideInt2Arr(int num, int count)
        {
            var arr = new int[count];
            var div = num / count;
            var rest = num % count;
            for (var i = 0; i < count; i++)
            {
                arr[i] = div;
            }
            for (var i = 0; i < rest; i++)
            {
                arr[i] += 1;
            }
            return arr;
        }

        /// <summary>
        /// Calculates the angle between two points and the x-axis
        /// </summary>
        /// <param name="center">The center point</param>
        /// <param name="p">The target point</param>
        /// <returns>The angle in degrees</returns>
        public static double CalAngle(Point center, Point p) => Math.Atan2(p.Y - center.Y, p.X - center.X) * 180 / Math.PI;
    }
}
