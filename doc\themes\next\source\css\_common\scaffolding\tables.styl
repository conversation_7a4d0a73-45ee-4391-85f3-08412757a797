.table-container {
  margin: 20px 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

table {
  width: $table-width;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: $table-font-size;
}

table > tbody > tr {
  &:nth-of-type(odd) { background-color: $table-row-odd-bg-color; }
  &:hover { background-color: $table-row-hover-bg-color; }
}

caption, th, td {
  padding: $table-cell-padding;
  text-align: $table-content-alignment;
  vertical-align: $table-content-vertical;
  font-weight: normal;
}

th, td {
  border: 1px solid $table-border-color;
  border-bottom: 3px solid $table-cell-border-bottom-color;
}

th {
  padding-bottom: 10px;
  font-weight: $table-th-font-weight;
}

td {
  border-bottom-width: 1px;
}
