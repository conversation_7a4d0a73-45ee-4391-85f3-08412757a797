.copy-btn {
  display: inline-block;
  padding: 6px 12px;
  font-size: 13px;
  font-weight: 700;
  line-height: 20px;
  color: #333;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  if hexo-config('codeblock.copy_button.style') == 'flat' {
    background-color: #fff;
    border: none;
  }
  else {
    background-color: #eee;
    background-image: linear-gradient(#fcfcfc, #eee);
    border: 1px solid #d5d5d5;
    border-radius: 3px;
  }
  user-select: none;
  outline: 0;
}

.highlight-wrap .copy-btn {
  transition: opacity .3s ease-in-out;
  opacity: 0;
  padding: 2px 6px;
  position: absolute;
  if hexo-config('codeblock.copy_button.style') == 'flat' {
    right: 0px;
    height: 42px;
  }
  else {
    right: 4px;
    top: 8px;
  }
}

.highlight-wrap:hover .copy-btn,
.highlight-wrap .copy-btn:focus {
  opacity: 1;
}

.highlight-wrap {
  position: relative;
}
