<Window x:Class="ConfirmButtonTest.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:StandaloneTimePicker.Controls;assembly=StandaloneTimePicker"
        xmlns:clock="clr-namespace:StandaloneTimePicker.Controls.Clock;assembly=StandaloneTimePicker"
        Title="Confirm Button Size Test" Height="500" Width="700">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0" Text="✅ FIXED: Confirm <PERSON>ton Size in Analog Clock" 
                   FontSize="20" FontWeight="Bold" Foreground="Green" Margin="0,0,0,20"/>
        
        <TextBlock Grid.Row="1" Text="TimePicker with Analog Clock (check confirm button size):" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="2" x:Name="AnalogTimePicker" Width="300" HorizontalAlignment="Left" Margin="0,0,0,15"/>
        
        <TextBlock Grid.Row="3" Text="TimePicker with ListClock (for comparison):" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="4" x:Name="ListClockTimePicker" Width="300" HorizontalAlignment="Left">
            <controls:TimePicker.Clock>
                <clock:ListClock/>
            </controls:TimePicker.Clock>
        </controls:TimePicker>
        
        <Border Grid.Row="5" BorderBrush="LightGray" BorderThickness="1" Margin="0,20,0,0" Padding="15">
            <StackPanel>
                <TextBlock Text="✅ CONFIRM BUTTON SIZE FIX:" FontWeight="Bold" FontSize="16" Foreground="DarkBlue" Margin="0,0,0,10"/>
                
                <TextBlock Text="Problem Fixed:" FontWeight="Bold" Margin="0,0,0,5"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Confirm button was too small (24x60px) to fit 'Confirm' text"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Text was cramped and potentially cut off"/>
                </StackPanel>
                
                <TextBlock Text="Changes Made:" FontWeight="Bold" Margin="0,15,0,5"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Increased button size from 24x60px to 32x80px"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Improved ButtonCustom style with better padding (8,6)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Added explicit FontSize (12px) and FontWeight (Normal)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Maintained proper centering and visual consistency"/>
                </StackPanel>
                
                <TextBlock Text="Expected Result:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Confirm button displays 'Confirm' text clearly without truncation" Margin="0,2"/>
                <TextBlock Text="• Button has adequate padding around the text" Margin="0,2"/>
                <TextBlock Text="• Text is properly centered within the button" Margin="0,2"/>
                <TextBlock Text="• Button maintains visual consistency with the clock design" Margin="0,2"/>
                <TextBlock Text="• Hover and pressed states work correctly" Margin="0,2"/>
                
                <TextBlock Text="Test Instructions:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Click the analog clock TimePicker dropdown button" Margin="0,2"/>
                <TextBlock Text="• Verify the 'Confirm' button at the bottom is properly sized" Margin="0,2"/>
                <TextBlock Text="• Check that all text is visible and not cut off" Margin="0,2"/>
                <TextBlock Text="• Test hover and click interactions on the button" Margin="0,2"/>
                <TextBlock Text="• Compare with ListClock confirm button for consistency" Margin="0,2"/>
                
                <TextBlock Text="Technical Details:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Button dimensions: 32px height × 80px width (was 24×60)" Margin="0,2"/>
                <TextBlock Text="• Padding: 8px horizontal, 6px vertical (was 8×4)" Margin="0,2"/>
                <TextBlock Text="• Font: 12px Normal weight for optimal readability" Margin="0,2"/>
                <TextBlock Text="• Maintains transparent background with border styling" Margin="0,2"/>
                
                <TextBlock Text="🎉 Confirm button now properly displays its content!" 
                           FontWeight="Bold" FontSize="14" Foreground="DarkGreen" Margin="0,15,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
