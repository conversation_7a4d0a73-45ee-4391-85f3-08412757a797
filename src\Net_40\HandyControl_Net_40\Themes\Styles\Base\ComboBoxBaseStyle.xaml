﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:hc="clr-namespace:HandyControl.Controls"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">
    
    <!--默认下拉框模板-->
    <ControlTemplate x:Key="ComboBoxTemplate" TargetType="ComboBox">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <Border x:Name="border" Grid.ColumnSpan="2" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" />
            <hc:ToggleBlock Grid.Column="1" Padding="1,0" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                <hc:ToggleBlock.UnCheckedContent>
                    <Path Data="{StaticResource DownGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                </hc:ToggleBlock.UnCheckedContent>
                <hc:ToggleBlock.CheckedContent>
                    <Path Data="{StaticResource UpGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                </hc:ToggleBlock.CheckedContent>
            </hc:ToggleBlock>
            <hc:ToggleBlock Grid.Column="0" Grid.ColumnSpan="2" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" />
            <ContentPresenter Grid.Column="0" Margin="{TemplateBinding Padding}" x:Name="contentPresenter" ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}" ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}" Content="{TemplateBinding SelectionBoxItem}" ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" IsHitTestVisible="false" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
            <Popup x:Name="PART_Popup" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" IsOpen="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" Margin="1" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                <Decorator Margin="8 0">
                    <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                        <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                            <hc:ToggleBlock.CheckedContent>
                                <ScrollViewer Margin="2">
                                    <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                </ScrollViewer>
                            </hc:ToggleBlock.CheckedContent>
                            <hc:ToggleBlock.UnCheckedContent>
                                <hc:Empty />
                            </hc:ToggleBlock.UnCheckedContent>
                        </hc:ToggleBlock>
                    </Border>
                </Decorator>
            </Popup>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.ConsistentWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.AutoWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualHeight, ElementName=border}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true"/>
                    <Condition Property="IsOpen" Value="false" SourceName="PART_Popup"/>
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}"/>
            </MultiTrigger>
            <Trigger Property="IsOpen" Value="True" SourceName="PART_Popup">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value="0.4"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--标题在顶部的下拉框模板-->
    <ControlTemplate x:Key="ComboBoxTopTemplate" TargetType="ComboBox">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition MinHeight="{Binding Path=(hc:InfoElement.MinContentHeight),RelativeSource={RelativeSource TemplatedParent}}" Height="{Binding Path=(hc:InfoElement.ContentHeight),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Double2GridLengthConverter}}"/>
            </Grid.RowDefinitions>
            <DockPanel LastChildFill="True" Visibility="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource String2VisibilityConverter}}" HorizontalAlignment="{Binding Path=(hc:TitleElement.HorizontalAlignment),RelativeSource={RelativeSource TemplatedParent}}" Margin="{Binding Path=(hc:TitleElement.MarginOnTheTop),RelativeSource={RelativeSource TemplatedParent}}">
                <ContentPresenter DockPanel.Dock="Right" TextElement.Foreground="{DynamicResource DangerBrush}" Margin="4,0,0,0" Content="{Binding Path=(hc:InfoElement.Symbol),RelativeSource={RelativeSource TemplatedParent}}" Visibility="{Binding Path=(hc:InfoElement.Necessary),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Boolean2VisibilityConverter}}" />
                <TextBlock hc:TextBlockAttach.AutoTooltip="True" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis" Text="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent}}" />
            </DockPanel>
            <Grid x:Name="contentPanel" Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Border x:Name="border" Grid.ColumnSpan="2" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" />
                <TextBlock Grid.Column="0" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Margin="{TemplateBinding Padding}" Visibility="{TemplateBinding Text,Converter={StaticResource String2VisibilityReConverter}}" HorizontalAlignment="Stretch" Style="{StaticResource TextBlockDefaultThiLight}" Text="{Binding Path=(hc:InfoElement.Placeholder),RelativeSource={RelativeSource TemplatedParent}}" />
                <hc:ToggleBlock Grid.Column="1" Padding="1,0" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                    <hc:ToggleBlock.UnCheckedContent>
                        <Path Data="{StaticResource DownGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.UnCheckedContent>
                    <hc:ToggleBlock.CheckedContent>
                        <Path Data="{StaticResource UpGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.CheckedContent>
                </hc:ToggleBlock>
                <hc:ToggleBlock Grid.Column="0" Grid.ColumnSpan="2" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" />
                <ContentPresenter Grid.Column="0" Margin="{TemplateBinding Padding}" x:Name="contentPresenter" ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}" ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}" Content="{TemplateBinding SelectionBoxItem}" ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" IsHitTestVisible="false" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                <Popup x:Name="PART_Popup" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" IsOpen="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" Margin="1" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                    <Decorator Margin="8 0">
                        <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                            <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                                <hc:ToggleBlock.CheckedContent>
                                    <ScrollViewer Margin="2">
                                        <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </hc:ToggleBlock.CheckedContent>
                                <hc:ToggleBlock.UnCheckedContent>
                                    <hc:Empty />
                                </hc:ToggleBlock.UnCheckedContent>
                            </hc:ToggleBlock>
                        </Border>
                    </Decorator>
                </Popup>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.ConsistentWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.AutoWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualHeight, ElementName=border}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel"/>
                    <Condition Property="IsOpen" Value="false" SourceName="PART_Popup"/>
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}"/>
            </MultiTrigger>
            <Trigger Property="IsOpen" Value="True" SourceName="PART_Popup">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value="0.4" TargetName="contentPanel" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--标题在左侧的下拉框模板-->
    <ControlTemplate x:Key="ComboBoxLeftTemplate" TargetType="ComboBox">
        <Grid MinHeight="{Binding Path=(hc:InfoElement.MinContentHeight),RelativeSource={RelativeSource TemplatedParent}}" Height="{Binding Path=(hc:InfoElement.ContentHeight),RelativeSource={RelativeSource TemplatedParent}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="{Binding Path=(hc:InfoElement.TitleWidth),RelativeSource={RelativeSource TemplatedParent}}"/>
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <DockPanel LastChildFill="True" Visibility="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource String2VisibilityConverter}}" HorizontalAlignment="{Binding Path=(hc:TitleElement.HorizontalAlignment),RelativeSource={RelativeSource TemplatedParent}}" VerticalAlignment="{Binding Path=(hc:TitleElement.VerticalAlignment),RelativeSource={RelativeSource TemplatedParent}}" Margin="{Binding Path=(hc:TitleElement.MarginOnTheLeft),RelativeSource={RelativeSource TemplatedParent}}">
                <ContentPresenter DockPanel.Dock="Right" TextElement.Foreground="{DynamicResource DangerBrush}" Margin="4,0,0,0" Content="{Binding Path=(hc:InfoElement.Symbol),RelativeSource={RelativeSource TemplatedParent}}" Visibility="{Binding Path=(hc:InfoElement.Necessary),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Boolean2VisibilityConverter}}"/>
                <TextBlock hc:TextBlockAttach.AutoTooltip="True" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis" Text="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent}}"/>
            </DockPanel>
            <Grid x:Name="contentPanel" Grid.Column="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Border x:Name="border" Grid.ColumnSpan="2" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" />
                <TextBlock Grid.Column="0" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Margin="{TemplateBinding Padding}" Visibility="{TemplateBinding Text,Converter={StaticResource String2VisibilityReConverter}}" HorizontalAlignment="Stretch" Style="{StaticResource TextBlockDefaultThiLight}" Text="{Binding Path=(hc:InfoElement.Placeholder),RelativeSource={RelativeSource TemplatedParent}}" />
                <hc:ToggleBlock Grid.Column="1" Padding="1,0" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                    <hc:ToggleBlock.UnCheckedContent>
                        <Path Data="{StaticResource DownGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.UnCheckedContent>
                    <hc:ToggleBlock.CheckedContent>
                        <Path Data="{StaticResource UpGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.CheckedContent>
                </hc:ToggleBlock>
                <hc:ToggleBlock Grid.Column="0" Grid.ColumnSpan="2" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" />
                <ContentPresenter Grid.Column="0" Margin="{TemplateBinding Padding}" x:Name="contentPresenter" ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}" ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}" Content="{TemplateBinding SelectionBoxItem}" ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" IsHitTestVisible="false" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                <Popup x:Name="PART_Popup" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" IsOpen="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" Margin="1" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                    <Decorator Margin="8 0">
                        <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                            <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                                <hc:ToggleBlock.CheckedContent>
                                    <ScrollViewer Margin="2">
                                        <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </hc:ToggleBlock.CheckedContent>
                                <hc:ToggleBlock.UnCheckedContent>
                                    <hc:Empty />
                                </hc:ToggleBlock.UnCheckedContent>
                            </hc:ToggleBlock>
                        </Border>
                    </Decorator>
                </Popup>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95" />
            </Trigger>
            <Trigger Property="hc:DropDownElement.ConsistentWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}" />
            </Trigger>
            <Trigger Property="hc:DropDownElement.AutoWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualHeight, ElementName=border}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="IsOpen" Value="false" SourceName="PART_Popup" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <Trigger Property="IsOpen" Value="True" SourceName="PART_Popup">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value="0.4" TargetName="contentPanel" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--可编辑下拉框中的输入框-->
    <Style x:Key="ComboBoxEditableTextBox" TargetType="TextBox">
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="AllowDrop" Value="true"/>
        <Setter Property="MinWidth" Value="0"/>
        <Setter Property="MinHeight" Value="0"/>
        <Setter Property="Padding" Value="{StaticResource DefaultControlPadding}"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <ScrollViewer Margin="-2,0,0,0" Padding="{TemplateBinding Padding}" x:Name="PART_ContentHost" Background="Transparent" Focusable="false" HorizontalScrollBarVisibility="Hidden" VerticalScrollBarVisibility="Hidden"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--默认可编辑下拉框模板-->
    <ControlTemplate x:Key="ComboBoxEditableTemplate" TargetType="ComboBox">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            <Border x:Name="border" Grid.ColumnSpan="2" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" />
            <hc:ToggleBlock Grid.Column="1" Padding="1,0" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                <hc:ToggleBlock.UnCheckedContent>
                    <Path Data="{StaticResource DownGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                </hc:ToggleBlock.UnCheckedContent>
                <hc:ToggleBlock.CheckedContent>
                    <Path Data="{StaticResource UpGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                </hc:ToggleBlock.CheckedContent>
            </hc:ToggleBlock>
            <TextBox x:Name="PART_EditableTextBox" Grid.Column="0" Padding="{TemplateBinding Padding}" Background="{TemplateBinding Background}" HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}" IsReadOnly="{Binding IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}" Style="{StaticResource ComboBoxEditableTextBox}" VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
            <Popup x:Name="PART_Popup" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" IsOpen="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" Margin="1" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                <Decorator Margin="8 0">
                    <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                        <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                            <hc:ToggleBlock.CheckedContent>
                                <ScrollViewer Margin="2">
                                    <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                </ScrollViewer>
                            </hc:ToggleBlock.CheckedContent>
                            <hc:ToggleBlock.UnCheckedContent>
                                <hc:Empty />
                            </hc:ToggleBlock.UnCheckedContent>
                        </hc:ToggleBlock>
                    </Border>
                </Decorator>
            </Popup>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.ConsistentWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.AutoWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualHeight, ElementName=border}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true"/>
                    <Condition Property="IsFocused" Value="false" SourceName="PART_EditableTextBox"/>
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}"/>
            </MultiTrigger>
            <Trigger Property="IsFocused" Value="True" SourceName="PART_EditableTextBox">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value="0.4"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--标题在顶部的可编辑下拉框模板-->
    <ControlTemplate x:Key="ComboBoxEditableTopTemplate" TargetType="ComboBox">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition MinHeight="{Binding Path=(hc:InfoElement.MinContentHeight),RelativeSource={RelativeSource TemplatedParent}}" Height="{Binding Path=(hc:InfoElement.ContentHeight),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Double2GridLengthConverter}}" />
            </Grid.RowDefinitions>
            <DockPanel LastChildFill="True" Visibility="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource String2VisibilityConverter}}" HorizontalAlignment="{Binding Path=(hc:TitleElement.HorizontalAlignment),RelativeSource={RelativeSource TemplatedParent}}" Margin="{Binding Path=(hc:TitleElement.MarginOnTheTop),RelativeSource={RelativeSource TemplatedParent}}">
                <ContentPresenter DockPanel.Dock="Right" TextElement.Foreground="{DynamicResource DangerBrush}" Margin="4,0,0,0" Content="{Binding Path=(hc:InfoElement.Symbol),RelativeSource={RelativeSource TemplatedParent}}" Visibility="{Binding Path=(hc:InfoElement.Necessary),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Boolean2VisibilityConverter}}"/>
                <TextBlock hc:TextBlockAttach.AutoTooltip="True" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis" Text="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent}}"/>
            </DockPanel>
            <Grid x:Name="contentPanel" Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Border x:Name="border" Grid.ColumnSpan="2" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" />
                <hc:ToggleBlock Grid.Column="1" Padding="1,0" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                    <hc:ToggleBlock.UnCheckedContent>
                        <Path Data="{StaticResource DownGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.UnCheckedContent>
                    <hc:ToggleBlock.CheckedContent>
                        <Path Data="{StaticResource UpGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.CheckedContent>
                </hc:ToggleBlock>
                <TextBlock VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Margin="{TemplateBinding Padding}" Visibility="{TemplateBinding Text,Converter={StaticResource String2VisibilityReConverter}}" HorizontalAlignment="Stretch" Style="{StaticResource TextBlockDefaultThiLight}" Text="{Binding Path=(hc:InfoElement.Placeholder),RelativeSource={RelativeSource TemplatedParent}}" />
                <TextBox x:Name="PART_EditableTextBox" Grid.Column="0" Padding="{TemplateBinding Padding}" HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}" IsReadOnly="{Binding IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}" Style="{StaticResource ComboBoxEditableTextBox}" VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                <Popup x:Name="PART_Popup" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                    <Decorator Margin="8 0">
                        <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                            <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                                <hc:ToggleBlock.CheckedContent>
                                    <ScrollViewer Margin="2">
                                        <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </hc:ToggleBlock.CheckedContent>
                                <hc:ToggleBlock.UnCheckedContent>
                                    <hc:Empty />
                                </hc:ToggleBlock.UnCheckedContent>
                            </hc:ToggleBlock>
                        </Border>
                    </Decorator>
                </Popup>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.ConsistentWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.AutoWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualHeight, ElementName=border}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel"/>
                    <Condition Property="IsFocused" Value="false" SourceName="PART_EditableTextBox"/>
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}"/>
            </MultiTrigger>
            <Trigger Property="IsFocused" Value="True" SourceName="PART_EditableTextBox">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value="0.4" TargetName="contentPanel" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--标题在左侧的可编辑下拉框模板-->
    <ControlTemplate x:Key="ComboBoxEditableLeftTemplate" TargetType="ComboBox">
        <Grid MinHeight="{Binding Path=(hc:InfoElement.MinContentHeight),RelativeSource={RelativeSource TemplatedParent}}" Height="{Binding Path=(hc:InfoElement.ContentHeight),RelativeSource={RelativeSource TemplatedParent}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="{Binding Path=(hc:InfoElement.TitleWidth),RelativeSource={RelativeSource TemplatedParent}}"/>
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <DockPanel LastChildFill="True" Visibility="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource String2VisibilityConverter}}" HorizontalAlignment="{Binding Path=(hc:TitleElement.HorizontalAlignment),RelativeSource={RelativeSource TemplatedParent}}" VerticalAlignment="{Binding Path=(hc:TitleElement.VerticalAlignment),RelativeSource={RelativeSource TemplatedParent}}" Margin="{Binding Path=(hc:TitleElement.MarginOnTheLeft),RelativeSource={RelativeSource TemplatedParent}}">
                <ContentPresenter DockPanel.Dock="Right" TextElement.Foreground="{DynamicResource DangerBrush}" Margin="4,0,0,0" Content="{Binding Path=(hc:InfoElement.Symbol),RelativeSource={RelativeSource TemplatedParent}}" Visibility="{Binding Path=(hc:InfoElement.Necessary),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Boolean2VisibilityConverter}}"/>
                <TextBlock hc:TextBlockAttach.AutoTooltip="True" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis" Text="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent}}"/>
            </DockPanel>
            <Grid x:Name="contentPanel" Grid.Column="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Border x:Name="border" Grid.ColumnSpan="2" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" />
                <hc:ToggleBlock Grid.Column="1" Padding="1,0" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                    <hc:ToggleBlock.UnCheckedContent>
                        <Path Data="{StaticResource DownGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.UnCheckedContent>
                    <hc:ToggleBlock.CheckedContent>
                        <Path Data="{StaticResource UpGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.CheckedContent>
                </hc:ToggleBlock>
                <TextBlock VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Margin="{TemplateBinding Padding}" Visibility="{TemplateBinding Text,Converter={StaticResource String2VisibilityReConverter}}" HorizontalAlignment="Stretch" Style="{StaticResource TextBlockDefaultThiLight}" Text="{Binding Path=(hc:InfoElement.Placeholder),RelativeSource={RelativeSource TemplatedParent}}" />
                <TextBox x:Name="PART_EditableTextBox" Grid.Column="0" Padding="{TemplateBinding Padding}" HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}" IsReadOnly="{Binding IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}" Style="{StaticResource ComboBoxEditableTextBox}" VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                <Popup x:Name="PART_Popup" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                    <Decorator Margin="8 0">
                        <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                            <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                                <hc:ToggleBlock.CheckedContent>
                                    <ScrollViewer Margin="2">
                                        <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </hc:ToggleBlock.CheckedContent>
                                <hc:ToggleBlock.UnCheckedContent>
                                    <hc:Empty />
                                </hc:ToggleBlock.UnCheckedContent>
                            </hc:ToggleBlock>
                        </Border>
                    </Decorator>
                </Popup>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95" />
            </Trigger>
            <Trigger Property="hc:DropDownElement.ConsistentWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}" />
            </Trigger>
            <Trigger Property="hc:DropDownElement.AutoWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualHeight, ElementName=border}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="IsFocused" Value="false" SourceName="PART_EditableTextBox" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <Trigger Property="IsFocused" Value="True" SourceName="PART_EditableTextBox">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value="0.4" TargetName="contentPanel" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--下拉项基样式-->
    <Style x:Key="ComboBoxItemBaseStyle" TargetType="ComboBoxItem">
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="Padding" Value="{StaticResource DefaultControlPadding}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="MinHeight" Value="{StaticResource DefaultControlHeight}"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBoxItem">
                    <Border x:Name="Bd" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" Padding="{TemplateBinding Padding}" >
                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="False"/>
                                <Condition Property="IsMouseOver" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="Bd" Value="{DynamicResource SecondaryRegionBrush}"/>
                        </MultiTrigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="Bd" Value="{DynamicResource PrimaryBrush}"/>
                            <Setter Property="Foreground" Value="White"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--下拉框基样式-->
    <Style x:Key="ComboBoxBaseStyle" BasedOn="{StaticResource InputElementBaseStyle}" TargetType="ComboBox">
        <Setter Property="ItemContainerStyle" Value="{StaticResource ComboBoxItemBaseStyle}"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="ScrollViewer.CanContentScroll" Value="true"/>
        <Setter Property="ScrollViewer.PanningMode" Value="Both"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="Template" Value="{StaticResource ComboBoxTemplate}"/>
        <Style.Triggers>
            <Trigger Property="IsEditable" Value="true">
                <Setter Property="IsTabStop" Value="false"/>
                <Setter Property="Template" Value="{StaticResource ComboBoxEditableTemplate}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--下拉框扩展样式-->
    <Style x:Key="ComboBoxExtendBaseStyle" BasedOn="{StaticResource ComboBoxBaseStyle}" TargetType="ComboBox">
        <Setter Property="Height" Value="Auto"/>
        <Setter Property="Template" Value="{StaticResource ComboBoxTopTemplate}"/>
        <Setter Property="hc:InfoElement.Symbol" Value="●"/>
        <Setter Property="hc:InfoElement.ContentHeight" Value="{x:Static system:Double.NaN}"/>
        <Setter Property="hc:InfoElement.MinContentHeight" Value="{StaticResource DefaultControlHeight}"/>
        <Style.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsEditable" Value="False"/>
                    <Condition Property="hc:InfoElement.TitlePlacement" Value="Left"/>
                </MultiTrigger.Conditions>
                <Setter Property="Template" Value="{StaticResource ComboBoxLeftTemplate}"/>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsEditable" Value="True"/>
                    <Condition Property="hc:InfoElement.TitlePlacement" Value="Top"/>
                </MultiTrigger.Conditions>
                <Setter Property="Template" Value="{StaticResource ComboBoxEditableTopTemplate}"/>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsEditable" Value="True"/>
                    <Condition Property="hc:InfoElement.TitlePlacement" Value="Left"/>
                </MultiTrigger.Conditions>
                <Setter Property="Template" Value="{StaticResource ComboBoxEditableLeftTemplate}"/>
            </MultiTrigger>
        </Style.Triggers>
    </Style>

    <!--标题在顶部的Handy式下拉框模板-->
    <ControlTemplate x:Key="ComboBoxPlusTopTemplate" TargetType="hc:ComboBox">
        <Grid >
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition MinHeight="{Binding Path=(hc:InfoElement.MinContentHeight),RelativeSource={RelativeSource TemplatedParent}}" Height="{Binding Path=(hc:InfoElement.ContentHeight),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Double2GridLengthConverter}}" />
            </Grid.RowDefinitions>
            <DockPanel LastChildFill="True" Visibility="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource String2VisibilityConverter}}" HorizontalAlignment="{Binding Path=(hc:TitleElement.HorizontalAlignment),RelativeSource={RelativeSource TemplatedParent}}" Margin="{Binding Path=(hc:TitleElement.MarginOnTheTop),RelativeSource={RelativeSource TemplatedParent}}">
                <ContentPresenter DockPanel.Dock="Right" TextElement.Foreground="{DynamicResource DangerBrush}" Margin="4,0,0,0" Content="{Binding Path=(hc:InfoElement.Symbol),RelativeSource={RelativeSource TemplatedParent}}" Visibility="{Binding Path=(hc:InfoElement.Necessary),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Boolean2VisibilityConverter}}"/>
                <TextBlock hc:TextBlockAttach.AutoTooltip="True" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis" Text="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent}}"/>
            </DockPanel>
            <Grid x:Name="contentPanel" Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Border x:Name="border" Grid.ColumnSpan="3" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" />
                <TextBlock Grid.Column="0" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Margin="{TemplateBinding Padding}" Visibility="{TemplateBinding Text,Converter={StaticResource String2VisibilityReConverter}}" HorizontalAlignment="Stretch" Style="{StaticResource TextBlockDefaultThiLight}" Text="{Binding Path=(hc:InfoElement.Placeholder),RelativeSource={RelativeSource TemplatedParent}}" />
                <hc:ToggleBlock Grid.Column="2" Padding="1 0" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                    <hc:ToggleBlock.UnCheckedContent>
                        <Path Data="{StaticResource DownGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.UnCheckedContent>
                    <hc:ToggleBlock.CheckedContent>
                        <Path Data="{StaticResource UpGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.CheckedContent>
                </hc:ToggleBlock>
                <hc:ToggleBlock Grid.Column="0" Grid.ColumnSpan="3" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" />
                <ContentPresenter Grid.Column="0" Margin="{TemplateBinding Padding}" x:Name="contentPresenter" ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}" ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}" Content="{TemplateBinding SelectionBoxItem}" ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" IsHitTestVisible="false" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                <Button Height="Auto" Width="Auto" HorizontalContentAlignment="Left" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" hc:IconElement.Width="14" Command="interactivity:ControlCommands.Clear" Visibility="Collapsed" Name="ButtonClear" Grid.Column="1" Style="{StaticResource ButtonIcon}" Padding="0,0,2,0" hc:IconElement.Geometry="{StaticResource DeleteFillCircleGeometry}" Foreground="{Binding BorderBrush,ElementName=border}" />
                <Popup x:Name="PART_Popup" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" IsOpen="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" Margin="1" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                    <Decorator Margin="8 0">
                        <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                            <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                                <hc:ToggleBlock.CheckedContent>
                                    <ScrollViewer Margin="2">
                                        <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </hc:ToggleBlock.CheckedContent>
                                <hc:ToggleBlock.UnCheckedContent>
                                    <hc:Empty />
                                </hc:ToggleBlock.UnCheckedContent>
                            </hc:ToggleBlock>
                        </Border>
                    </Decorator>
                </Popup>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.ConsistentWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.AutoWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualHeight, ElementName=border}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="IsOpen" Value="false" SourceName="PART_Popup" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="ButtonClear" />
                    <Condition Property="IsOpen" Value="false" SourceName="PART_Popup" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="hc:InfoElement.ShowClearButton" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Visibility" Value="Visible" TargetName="ButtonClear" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="ButtonClear" />
                    <Condition Property="hc:InfoElement.ShowClearButton" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Visibility" Value="Visible" TargetName="ButtonClear" />
            </MultiTrigger>
            <Trigger Property="IsOpen" Value="True" SourceName="PART_Popup">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value="0.4" TargetName="contentPanel" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--标题在左侧的Handy式下拉框模板-->
    <ControlTemplate x:Key="ComboBoxPlusLeftTemplate" TargetType="hc:ComboBox">
        <Grid MinHeight="{Binding Path=(hc:InfoElement.MinContentHeight),RelativeSource={RelativeSource TemplatedParent}}" Height="{Binding Path=(hc:InfoElement.ContentHeight),RelativeSource={RelativeSource TemplatedParent}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="{Binding Path=(hc:InfoElement.TitleWidth),RelativeSource={RelativeSource TemplatedParent}}"/>
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <DockPanel LastChildFill="True" Visibility="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource String2VisibilityConverter}}" HorizontalAlignment="{Binding Path=(hc:TitleElement.HorizontalAlignment),RelativeSource={RelativeSource TemplatedParent}}" VerticalAlignment="{Binding Path=(hc:TitleElement.VerticalAlignment),RelativeSource={RelativeSource TemplatedParent}}" Margin="{Binding Path=(hc:TitleElement.MarginOnTheLeft),RelativeSource={RelativeSource TemplatedParent}}">
                <ContentPresenter DockPanel.Dock="Right" TextElement.Foreground="{DynamicResource DangerBrush}" Margin="4,0,0,0" Content="{Binding Path=(hc:InfoElement.Symbol),RelativeSource={RelativeSource TemplatedParent}}" Visibility="{Binding Path=(hc:InfoElement.Necessary),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Boolean2VisibilityConverter}}"/>
                <TextBlock hc:TextBlockAttach.AutoTooltip="True" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis" Text="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent}}"/>
            </DockPanel>
            <Grid x:Name="contentPanel" Grid.Column="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Border x:Name="border" Grid.ColumnSpan="3" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" />
                <TextBlock Grid.Column="0" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Margin="{TemplateBinding Padding}" Visibility="{TemplateBinding Text,Converter={StaticResource String2VisibilityReConverter}}" HorizontalAlignment="Stretch" Style="{StaticResource TextBlockDefaultThiLight}" Text="{Binding Path=(hc:InfoElement.Placeholder),RelativeSource={RelativeSource TemplatedParent}}" />
                <hc:ToggleBlock Grid.Column="2" Padding="1 0" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                    <hc:ToggleBlock.UnCheckedContent>
                        <Path Data="{StaticResource DownGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.UnCheckedContent>
                    <hc:ToggleBlock.CheckedContent>
                        <Path Data="{StaticResource UpGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.CheckedContent>
                </hc:ToggleBlock>
                <hc:ToggleBlock Grid.Column="0" Grid.ColumnSpan="3" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" />
                <ContentPresenter Grid.Column="0" Margin="{TemplateBinding Padding}" x:Name="contentPresenter" ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}" ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}" Content="{TemplateBinding SelectionBoxItem}" ContentStringFormat="{TemplateBinding SelectionBoxItemStringFormat}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" IsHitTestVisible="false" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                <Button Height="Auto" Width="Auto" HorizontalContentAlignment="Left" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" hc:IconElement.Width="14" Command="interactivity:ControlCommands.Clear" Visibility="Collapsed" Name="ButtonClear" Grid.Column="1" Style="{StaticResource ButtonIcon}" Padding="0,0,2,0" hc:IconElement.Geometry="{StaticResource DeleteFillCircleGeometry}" Foreground="{Binding BorderBrush,ElementName=border}" />
                <Popup x:Name="PART_Popup" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" IsOpen="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" Margin="1" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                    <Decorator Margin="8 0">
                        <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                            <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                                <hc:ToggleBlock.CheckedContent>
                                    <ScrollViewer Margin="2">
                                        <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </hc:ToggleBlock.CheckedContent>
                                <hc:ToggleBlock.UnCheckedContent>
                                    <hc:Empty />
                                </hc:ToggleBlock.UnCheckedContent>
                            </hc:ToggleBlock>
                        </Border>
                    </Decorator>
                </Popup>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95" />
            </Trigger>
            <Trigger Property="hc:DropDownElement.ConsistentWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}" />
            </Trigger>
            <Trigger Property="hc:DropDownElement.AutoWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualHeight, ElementName=border}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="IsOpen" Value="false" SourceName="PART_Popup" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="ButtonClear" />
                    <Condition Property="IsOpen" Value="false" SourceName="PART_Popup" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="hc:InfoElement.ShowClearButton" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Visibility" Value="Visible" TargetName="ButtonClear" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="ButtonClear" />
                    <Condition Property="hc:InfoElement.ShowClearButton" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Visibility" Value="Visible" TargetName="ButtonClear" />
            </MultiTrigger>
            <Trigger Property="IsOpen" Value="True" SourceName="PART_Popup">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value="0.4" TargetName="contentPanel" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--标题在顶部的Handy式可编辑下拉框模板-->
    <ControlTemplate x:Key="ComboBoxPlusEditableTopTemplate" TargetType="hc:ComboBox">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition MinHeight="{Binding Path=(hc:InfoElement.MinContentHeight),RelativeSource={RelativeSource TemplatedParent}}" Height="{Binding Path=(hc:InfoElement.ContentHeight),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Double2GridLengthConverter}}" />
            </Grid.RowDefinitions>
            <DockPanel Grid.ColumnSpan="2" LastChildFill="True" Visibility="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource String2VisibilityConverter}}" HorizontalAlignment="{Binding Path=(hc:TitleElement.HorizontalAlignment),RelativeSource={RelativeSource TemplatedParent}}" Margin="{Binding Path=(hc:TitleElement.MarginOnTheTop),RelativeSource={RelativeSource TemplatedParent}}">
                <ContentPresenter DockPanel.Dock="Right" TextElement.Foreground="{DynamicResource DangerBrush}" Margin="4,0,0,0" Content="{Binding Path=(hc:InfoElement.Symbol),RelativeSource={RelativeSource TemplatedParent}}" Visibility="{Binding Path=(hc:InfoElement.Necessary),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Boolean2VisibilityConverter}}"/>
                <TextBlock hc:TextBlockAttach.AutoTooltip="True" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis" Text="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent}}"/>
            </DockPanel>
            <Grid x:Name="contentPanel" Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Border x:Name="border" Grid.ColumnSpan="3" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" />
                <hc:ToggleBlock Grid.Column="2" Padding="1,0" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                    <hc:ToggleBlock.UnCheckedContent>
                        <Path Data="{StaticResource DownGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.UnCheckedContent>
                    <hc:ToggleBlock.CheckedContent>
                        <Path Data="{StaticResource UpGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.CheckedContent>
                </hc:ToggleBlock>
                <TextBlock VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Margin="{TemplateBinding Padding}" Visibility="{TemplateBinding Text,Converter={StaticResource String2VisibilityReConverter}}" HorizontalAlignment="Stretch" Style="{StaticResource TextBlockDefaultThiLight}" Text="{Binding Path=(hc:InfoElement.Placeholder),RelativeSource={RelativeSource TemplatedParent}}" />
                <TextBox x:Name="PART_EditableTextBox" Grid.Column="0" Padding="{TemplateBinding Padding}" HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}" IsReadOnly="{Binding IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}" Style="{StaticResource ComboBoxEditableTextBox}" VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                <Button Height="Auto" Width="Auto" HorizontalContentAlignment="Left" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" hc:IconElement.Width="14" Command="interactivity:ControlCommands.Clear" Visibility="Collapsed" Name="ButtonClear" Grid.Column="1" Style="{StaticResource ButtonIcon}" Padding="0,0,2,0" hc:IconElement.Geometry="{StaticResource DeleteFillCircleGeometry}" Foreground="{Binding BorderBrush,ElementName=border}" />
                <Popup x:Name="PART_Popup" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                    <Decorator Margin="8 0">
                        <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                            <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                                <hc:ToggleBlock.CheckedContent>
                                    <ScrollViewer Margin="2">
                                        <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </hc:ToggleBlock.CheckedContent>
                                <hc:ToggleBlock.UnCheckedContent>
                                    <hc:Empty />
                                </hc:ToggleBlock.UnCheckedContent>
                            </hc:ToggleBlock>
                        </Border>
                    </Decorator>
                </Popup>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95" />
            </Trigger>
            <Trigger Property="hc:DropDownElement.ConsistentWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}" />
            </Trigger>
            <Trigger Property="hc:DropDownElement.AutoWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualHeight, ElementName=border}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="IsFocused" Value="false" SourceName="PART_EditableTextBox" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="ButtonClear" />
                    <Condition Property="IsOpen" Value="false" SourceName="PART_Popup" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="hc:InfoElement.ShowClearButton" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Visibility" Value="Visible" TargetName="ButtonClear" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="ButtonClear" />
                    <Condition Property="hc:InfoElement.ShowClearButton" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Visibility" Value="Visible" TargetName="ButtonClear" />
            </MultiTrigger>
            <Trigger Property="IsFocused" Value="True" SourceName="PART_EditableTextBox">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value="0.4" TargetName="contentPanel" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="ComboBoxPlusEditableTopAutoCompleteTemplate" TargetType="hc:ComboBox">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition MinHeight="{Binding Path=(hc:InfoElement.MinContentHeight),RelativeSource={RelativeSource TemplatedParent}}" Height="{Binding Path=(hc:InfoElement.ContentHeight),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Double2GridLengthConverter}}" />
            </Grid.RowDefinitions>
            <DockPanel Grid.ColumnSpan="2" LastChildFill="True" Visibility="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource String2VisibilityConverter}}" HorizontalAlignment="{Binding Path=(hc:TitleElement.HorizontalAlignment),RelativeSource={RelativeSource TemplatedParent}}" Margin="{Binding Path=(hc:TitleElement.MarginOnTheTop),RelativeSource={RelativeSource TemplatedParent}}">
                <ContentPresenter DockPanel.Dock="Right" TextElement.Foreground="{DynamicResource DangerBrush}" Margin="4,0,0,0" Content="{Binding Path=(hc:InfoElement.Symbol),RelativeSource={RelativeSource TemplatedParent}}" Visibility="{Binding Path=(hc:InfoElement.Necessary),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Boolean2VisibilityConverter}}" />
                <TextBlock hc:TextBlockAttach.AutoTooltip="True" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis" Text="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent}}" />
            </DockPanel>
            <Grid x:Name="contentPanel" Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Border x:Name="border" Grid.ColumnSpan="3" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" />
                <hc:ToggleBlock Grid.Column="2" Padding="1,0" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                    <hc:ToggleBlock.UnCheckedContent>
                        <Path Data="{StaticResource DownGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.UnCheckedContent>
                    <hc:ToggleBlock.CheckedContent>
                        <Path Data="{StaticResource UpGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.CheckedContent>
                </hc:ToggleBlock>
                <TextBlock VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Margin="{TemplateBinding Padding}" Visibility="{TemplateBinding Text,Converter={StaticResource String2VisibilityReConverter}}" HorizontalAlignment="Stretch" Style="{StaticResource TextBlockDefaultThiLight}" Text="{Binding Path=(hc:InfoElement.Placeholder),RelativeSource={RelativeSource TemplatedParent}}" />
                <TextBox x:Name="PART_EditableTextBox" Grid.Column="0" Padding="{TemplateBinding Padding}" HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}" IsReadOnly="{Binding IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}" Style="{StaticResource ComboBoxEditableTextBox}" VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                <Button Height="Auto" Width="Auto" HorizontalContentAlignment="Left" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" hc:IconElement.Width="14" Command="interactivity:ControlCommands.Clear" Visibility="Collapsed" Name="ButtonClear" Grid.Column="1" Style="{StaticResource ButtonIcon}" Padding="0,0,2,0" hc:IconElement.Geometry="{StaticResource DeleteFillCircleGeometry}" Foreground="{Binding BorderBrush,ElementName=border}" />
                <Popup x:Name="PART_Popup" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                    <Decorator Margin="8 0">
                        <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                            <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                                <hc:ToggleBlock.CheckedContent>
                                    <ScrollViewer Margin="2">
                                        <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </hc:ToggleBlock.CheckedContent>
                                <hc:ToggleBlock.UnCheckedContent>
                                    <hc:Empty />
                                </hc:ToggleBlock.UnCheckedContent>
                            </hc:ToggleBlock>
                        </Border>
                    </Decorator>
                </Popup>
                <Popup x:Name="PART_Popup_AutoComplete" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                    <Decorator Margin="8 0">
                        <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder_AutoComplete" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                            <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                                <hc:ToggleBlock.CheckedContent>
                                    <ScrollViewer Margin="2">
                                        <StackPanel Name="PART_AutoCompletePanel" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </hc:ToggleBlock.CheckedContent>
                                <hc:ToggleBlock.UnCheckedContent>
                                    <hc:Empty />
                                </hc:ToggleBlock.UnCheckedContent>
                            </hc:ToggleBlock>
                        </Border>
                    </Decorator>
                </Popup>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95"/>
                <Setter Property="Height" TargetName="dropDownBorder_AutoComplete" Value="95"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.ConsistentWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
                <Setter Property="MaxWidth" TargetName="dropDownBorder_AutoComplete" Value="{Binding ActualWidth, ElementName=border}"/>
                <Setter Property="MinWidth" TargetName="dropDownBorder_AutoComplete" Value="{Binding ActualWidth, ElementName=border}"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.AutoWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualHeight, ElementName=border}" />
                <Setter Property="MaxWidth" TargetName="dropDownBorder_AutoComplete" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder_AutoComplete" Value="{Binding ActualHeight, ElementName=border}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="IsFocused" Value="false" SourceName="PART_EditableTextBox" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="ButtonClear" />
                    <Condition Property="IsOpen" Value="false" SourceName="PART_Popup" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="hc:InfoElement.ShowClearButton" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Visibility" Value="Visible" TargetName="ButtonClear" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="ButtonClear" />
                    <Condition Property="hc:InfoElement.ShowClearButton" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Visibility" Value="Visible" TargetName="ButtonClear" />
            </MultiTrigger>
            <Trigger Property="IsFocused" Value="True" SourceName="PART_EditableTextBox">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value="0.4" TargetName="contentPanel" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--标题在左侧的Handy式可编辑下拉框模板-->
    <ControlTemplate x:Key="ComboBoxPlusEditableLeftTemplate" TargetType="hc:ComboBox">
        <Grid MinHeight="{Binding Path=(hc:InfoElement.MinContentHeight),RelativeSource={RelativeSource TemplatedParent}}" Height="{Binding Path=(hc:InfoElement.ContentHeight),RelativeSource={RelativeSource TemplatedParent}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="{Binding Path=(hc:InfoElement.TitleWidth),RelativeSource={RelativeSource TemplatedParent}}"/>
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <DockPanel LastChildFill="True" Visibility="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource String2VisibilityConverter}}" HorizontalAlignment="{Binding Path=(hc:TitleElement.HorizontalAlignment),RelativeSource={RelativeSource TemplatedParent}}" VerticalAlignment="{Binding Path=(hc:TitleElement.VerticalAlignment),RelativeSource={RelativeSource TemplatedParent}}" Margin="{Binding Path=(hc:TitleElement.MarginOnTheLeft),RelativeSource={RelativeSource TemplatedParent}}">
                <ContentPresenter DockPanel.Dock="Right" TextElement.Foreground="{DynamicResource DangerBrush}" Margin="4,0,0,0" Content="{Binding Path=(hc:InfoElement.Symbol),RelativeSource={RelativeSource TemplatedParent}}" Visibility="{Binding Path=(hc:InfoElement.Necessary),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Boolean2VisibilityConverter}}"/>
                <TextBlock hc:TextBlockAttach.AutoTooltip="True" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis" Text="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent}}"/>
            </DockPanel>
            <Grid x:Name="contentPanel" Grid.Column="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Border x:Name="border" Grid.ColumnSpan="3" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" />
                <hc:ToggleBlock Grid.Column="2" Padding="1,0" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                    <hc:ToggleBlock.UnCheckedContent>
                        <Path Data="{StaticResource DownGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.UnCheckedContent>
                    <hc:ToggleBlock.CheckedContent>
                        <Path Data="{StaticResource UpGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.CheckedContent>
                </hc:ToggleBlock>
                <TextBlock VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Margin="{TemplateBinding Padding}" Visibility="{TemplateBinding Text,Converter={StaticResource String2VisibilityReConverter}}" HorizontalAlignment="Stretch" Style="{StaticResource TextBlockDefaultThiLight}" Text="{Binding Path=(hc:InfoElement.Placeholder),RelativeSource={RelativeSource TemplatedParent}}" />
                <TextBox x:Name="PART_EditableTextBox" Grid.Column="0" Padding="{TemplateBinding Padding}" HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}" IsReadOnly="{Binding IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}" Style="{StaticResource ComboBoxEditableTextBox}" VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                <Button Height="Auto" Width="Auto" HorizontalContentAlignment="Left" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" hc:IconElement.Width="14" Command="interactivity:ControlCommands.Clear" Visibility="Collapsed" Name="ButtonClear" Grid.Column="1" Style="{StaticResource ButtonIcon}" Padding="0,0,2,0" hc:IconElement.Geometry="{StaticResource DeleteFillCircleGeometry}" Foreground="{Binding BorderBrush,ElementName=border}" />
                <Popup x:Name="PART_Popup" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                    <Decorator Margin="8 0">
                        <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                            <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                                <hc:ToggleBlock.CheckedContent>
                                    <ScrollViewer Margin="2">
                                        <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </hc:ToggleBlock.CheckedContent>
                                <hc:ToggleBlock.UnCheckedContent>
                                    <hc:Empty />
                                </hc:ToggleBlock.UnCheckedContent>
                            </hc:ToggleBlock>
                        </Border>
                    </Decorator>
                </Popup>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95" />
            </Trigger>
            <Trigger Property="hc:DropDownElement.ConsistentWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}" />
            </Trigger>
            <Trigger Property="hc:DropDownElement.AutoWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualHeight, ElementName=border}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="IsFocused" Value="false" SourceName="PART_EditableTextBox" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="ButtonClear" />
                    <Condition Property="IsOpen" Value="false" SourceName="PART_Popup" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="hc:InfoElement.ShowClearButton" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Visibility" Value="Visible" TargetName="ButtonClear" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="ButtonClear" />
                    <Condition Property="hc:InfoElement.ShowClearButton" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Visibility" Value="Visible" TargetName="ButtonClear" />
            </MultiTrigger>
            <Trigger Property="IsFocused" Value="True" SourceName="PART_EditableTextBox">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value="0.4" TargetName="contentPanel" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="ComboBoxPlusEditableLeftAutoCompleteTemplate" TargetType="hc:ComboBox">
        <Grid MinHeight="{Binding Path=(hc:InfoElement.MinContentHeight),RelativeSource={RelativeSource TemplatedParent}}" Height="{Binding Path=(hc:InfoElement.ContentHeight),RelativeSource={RelativeSource TemplatedParent}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="{Binding Path=(hc:InfoElement.TitleWidth),RelativeSource={RelativeSource TemplatedParent}}"/>
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <DockPanel LastChildFill="True" Visibility="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource String2VisibilityConverter}}" HorizontalAlignment="{Binding Path=(hc:TitleElement.HorizontalAlignment),RelativeSource={RelativeSource TemplatedParent}}" VerticalAlignment="{Binding Path=(hc:TitleElement.VerticalAlignment),RelativeSource={RelativeSource TemplatedParent}}" Margin="{Binding Path=(hc:TitleElement.MarginOnTheLeft),RelativeSource={RelativeSource TemplatedParent}}">
                <ContentPresenter DockPanel.Dock="Right" TextElement.Foreground="{DynamicResource DangerBrush}" Margin="4,0,0,0" Content="{Binding Path=(hc:InfoElement.Symbol),RelativeSource={RelativeSource TemplatedParent}}" Visibility="{Binding Path=(hc:InfoElement.Necessary),RelativeSource={RelativeSource TemplatedParent},Converter={StaticResource Boolean2VisibilityConverter}}"/>
                <TextBlock hc:TextBlockAttach.AutoTooltip="True" TextWrapping="NoWrap" TextTrimming="CharacterEllipsis" Text="{Binding Path=(hc:InfoElement.Title),RelativeSource={RelativeSource TemplatedParent}}"/>
            </DockPanel>
            <Grid x:Name="contentPanel" Grid.Column="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Border x:Name="border" Grid.ColumnSpan="3" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" />
                <hc:ToggleBlock Grid.Column="2" Padding="1,0" Background="Transparent" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch" ToggleGesture="LeftClick" IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                    <hc:ToggleBlock.UnCheckedContent>
                        <Path Data="{StaticResource DownGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.UnCheckedContent>
                    <hc:ToggleBlock.CheckedContent>
                        <Path Data="{StaticResource UpGeometry}" VerticalAlignment="Center" HorizontalAlignment="Right" Width="14" Stretch="Uniform" Fill="{TemplateBinding BorderBrush}" Margin="{Binding Padding, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource ThicknessSplitConverter}, ConverterParameter='0,0,1,0'}" />
                    </hc:ToggleBlock.CheckedContent>
                </hc:ToggleBlock>
                <TextBlock VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Margin="{TemplateBinding Padding}" Visibility="{TemplateBinding Text,Converter={StaticResource String2VisibilityReConverter}}" HorizontalAlignment="Stretch" Style="{StaticResource TextBlockDefaultThiLight}" Text="{Binding Path=(hc:InfoElement.Placeholder),RelativeSource={RelativeSource TemplatedParent}}" />
                <TextBox x:Name="PART_EditableTextBox" Grid.Column="0" Padding="{TemplateBinding Padding}" HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}" IsReadOnly="{Binding IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}" Style="{StaticResource ComboBoxEditableTextBox}" VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                <Button Height="Auto" Width="Auto" HorizontalContentAlignment="Left" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" hc:IconElement.Width="14" Command="interactivity:ControlCommands.Clear" Visibility="Collapsed" Name="ButtonClear" Grid.Column="1" Style="{StaticResource ButtonIcon}" Padding="0,0,2,0" hc:IconElement.Geometry="{StaticResource DeleteFillCircleGeometry}" Foreground="{Binding BorderBrush,ElementName=border}" />
                <Popup x:Name="PART_Popup" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                    <Decorator Margin="8 0">
                        <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                            <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                                <hc:ToggleBlock.CheckedContent>
                                    <ScrollViewer Margin="2">
                                        <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </hc:ToggleBlock.CheckedContent>
                                <hc:ToggleBlock.UnCheckedContent>
                                    <hc:Empty />
                                </hc:ToggleBlock.UnCheckedContent>
                            </hc:ToggleBlock>
                        </Border>
                    </Decorator>
                </Popup>
                <Popup x:Name="PART_Popup_AutoComplete" PlacementTarget="{Binding ElementName=border}" AllowsTransparency="true" PopupAnimation="{StaticResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" Placement="Bottom">
                    <Decorator Margin="8 0">
                        <Border BorderThickness="0,1,0,0" Effect="{StaticResource EffectShadow2}" Margin="0,0,0,8" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" x:Name="dropDownBorder_AutoComplete" MinWidth="{Binding ActualWidth, ElementName=border}" MaxHeight="{TemplateBinding MaxDropDownHeight}" BorderBrush="{DynamicResource BorderBrush}" Background="{DynamicResource RegionBrush}">
                            <hc:ToggleBlock IsChecked="{Binding HasItems,RelativeSource={RelativeSource TemplatedParent},Mode=OneWay}" VerticalContentAlignment="Stretch" HorizontalContentAlignment="Stretch">
                                <hc:ToggleBlock.CheckedContent>
                                    <ScrollViewer Margin="2">
                                        <StackPanel Name="PART_AutoCompletePanel" KeyboardNavigation.DirectionalNavigation="Contained" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                    </ScrollViewer>
                                </hc:ToggleBlock.CheckedContent>
                                <hc:ToggleBlock.UnCheckedContent>
                                    <hc:Empty />
                                </hc:ToggleBlock.UnCheckedContent>
                            </hc:ToggleBlock>
                        </Border>
                    </Decorator>
                </Popup>
            </Grid>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="Height" TargetName="dropDownBorder" Value="95"/>
                <Setter Property="Height" TargetName="dropDownBorder_AutoComplete" Value="95"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.ConsistentWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualWidth, ElementName=border}"/>
                <Setter Property="MaxWidth" TargetName="dropDownBorder_AutoComplete" Value="{Binding ActualWidth, ElementName=border}"/>
                <Setter Property="MinWidth" TargetName="dropDownBorder_AutoComplete" Value="{Binding ActualWidth, ElementName=border}"/>
            </Trigger>
            <Trigger Property="hc:DropDownElement.AutoWidth" Value="True">
                <Setter Property="MaxWidth" TargetName="dropDownBorder" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder" Value="{Binding ActualHeight, ElementName=border}" />
                <Setter Property="MaxWidth" TargetName="dropDownBorder_AutoComplete" Value="{x:Static system:Double.MaxValue}" />
                <Setter Property="MinWidth" TargetName="dropDownBorder_AutoComplete" Value="{Binding ActualHeight, ElementName=border}" />
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="IsFocused" Value="false" SourceName="PART_EditableTextBox" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="ButtonClear" />
                    <Condition Property="IsOpen" Value="false" SourceName="PART_Popup" />
                </MultiTrigger.Conditions>
                <Setter Property="BorderBrush" Value="{DynamicResource SecondaryBorderBrush}" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="contentPanel" />
                    <Condition Property="hc:InfoElement.ShowClearButton" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Visibility" Value="Visible" TargetName="ButtonClear" />
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="true" SourceName="ButtonClear" />
                    <Condition Property="hc:InfoElement.ShowClearButton" Value="True" />
                </MultiTrigger.Conditions>
                <Setter Property="Visibility" Value="Visible" TargetName="ButtonClear" />
            </MultiTrigger>
            <Trigger Property="IsFocused" Value="True" SourceName="PART_EditableTextBox">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Opacity" Value="0.4" TargetName="contentPanel" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--Handy式下拉框样式-->
    <Style x:Key="ComboBoxPlusBaseStyle" BasedOn="{StaticResource ComboBoxExtendBaseStyle}" TargetType="hc:ComboBox">
        <Setter Property="Template" Value="{StaticResource ComboBoxPlusTopTemplate}"/>
        <Style.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsEditable" Value="False"/>
                    <Condition Property="hc:InfoElement.TitlePlacement" Value="Left"/>
                </MultiTrigger.Conditions>
                <Setter Property="Template" Value="{StaticResource ComboBoxPlusLeftTemplate}"/>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsEditable" Value="True"/>
                    <Condition Property="hc:InfoElement.TitlePlacement" Value="Top"/>
                </MultiTrigger.Conditions>
                <Setter Property="Template" Value="{StaticResource ComboBoxPlusEditableTopTemplate}"/>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsEditable" Value="True"/>
                    <Condition Property="hc:InfoElement.TitlePlacement" Value="Top"/>
                    <Condition Property="AutoComplete" Value="True"/>
                </MultiTrigger.Conditions>
                <Setter Property="Template" Value="{StaticResource ComboBoxPlusEditableTopAutoCompleteTemplate}"/>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsEditable" Value="True"/>
                    <Condition Property="hc:InfoElement.TitlePlacement" Value="Left"/>
                </MultiTrigger.Conditions>
                <Setter Property="Template" Value="{StaticResource ComboBoxPlusEditableLeftTemplate}"/>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsEditable" Value="True"/>
                    <Condition Property="hc:InfoElement.TitlePlacement" Value="Left"/>
                    <Condition Property="AutoComplete" Value="True"/>
                </MultiTrigger.Conditions>
                <Setter Property="Template" Value="{StaticResource ComboBoxPlusEditableLeftAutoCompleteTemplate}"/>
            </MultiTrigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
