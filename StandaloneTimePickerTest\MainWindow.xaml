<Window x:Class="StandaloneTimePickerTest.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:StandaloneTimePicker.Controls;assembly=StandaloneTimePicker"
        xmlns:clock="clr-namespace:StandaloneTimePicker.Controls.Clock;assembly=StandaloneTimePicker"
        Title="Standalone TimePicker Test" Height="600" Width="800">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0" Text="Standalone TimePicker Component Test" 
                   FontSize="24" FontWeight="Bold" Margin="0,0,0,20" 
                   HorizontalAlignment="Center"/>
        
        <TextBlock Grid.Row="1" Text="Basic TimePicker:" FontWeight="Bold" Margin="0,10,0,5"/>
        <controls:TimePicker Grid.Row="2" x:Name="BasicTimePicker" Width="200" HorizontalAlignment="Left"/>
        
        <TextBlock Grid.Row="3" Text="TimePicker with ListClock:" FontWeight="Bold" Margin="0,20,0,5"/>
        <controls:TimePicker Grid.Row="4" x:Name="ListClockTimePicker" Width="200" HorizontalAlignment="Left">
            <controls:TimePicker.Clock>
                <clock:ListClock/>
            </controls:TimePicker.Clock>
        </controls:TimePicker>
        
        <TextBlock Grid.Row="5" Text="Selected Times:" FontWeight="Bold" Margin="0,20,0,5"/>
        <StackPanel Grid.Row="6" Orientation="Vertical">
            <TextBlock x:Name="BasicTimeDisplay" Margin="0,5"/>
            <TextBlock x:Name="ListClockTimeDisplay" Margin="0,5"/>
            <Button Content="Get Current Times" Click="GetTimes_Click" Width="150" HorizontalAlignment="Left" Margin="0,10"/>
        </StackPanel>
        
        <Border Grid.Row="7" BorderBrush="LightGray" BorderThickness="1" Margin="0,20,0,0" Padding="10">
            <StackPanel>
                <TextBlock Text="Instructions:" FontWeight="Bold" Margin="0,0,0,10"/>
                <TextBlock TextWrapping="Wrap" Margin="0,2">
                    • Click the dropdown arrow to open the time picker
                </TextBlock>
                <TextBlock TextWrapping="Wrap" Margin="0,2">
                    • Use the analog clock or list clock to select time
                </TextBlock>
                <TextBlock TextWrapping="Wrap" Margin="0,2">
                    • Type time directly in the text box (format: HH:mm:ss)
                </TextBlock>
                <TextBlock TextWrapping="Wrap" Margin="0,2">
                    • Press Enter to confirm typed time
                </TextBlock>
                <TextBlock TextWrapping="Wrap" Margin="0,2">
                    • Click "Get Current Times" to see selected values
                </TextBlock>
            </StackPanel>
        </Border>
    </Grid>
</Window>
