<Window x:Class="VisibleHoverScrollbarTest.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:StandaloneTimePicker.Controls;assembly=StandaloneTimePicker"
        xmlns:clock="clr-namespace:StandaloneTimePicker.Controls.Clock;assembly=StandaloneTimePicker"
        Title="FIXED: Visible Hover Expansion Scrollbars" Height="550" Width="750">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0" Text="✅ FIXED: Visible Hover Expansion Scrollbars" 
                   FontSize="20" FontWeight="Bold" Foreground="Green" Margin="0,0,0,20"/>
        
        <TextBlock Grid.Row="1" Text="TimePicker with ListClock (visible hover expansion scrollbars):" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="2" x:Name="ListClockTimePicker" Width="300" HorizontalAlignment="Left" Margin="0,0,0,15">
            <controls:TimePicker.Clock>
                <clock:ListClock/>
            </controls:TimePicker.Clock>
        </controls:TimePicker>
        
        <TextBlock Grid.Row="3" Text="Standard TimePicker with Analog Clock (for comparison):" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="4" x:Name="AnalogTimePicker" Width="300" HorizontalAlignment="Left"/>
        
        <Border Grid.Row="5" BorderBrush="LightGray" BorderThickness="1" Margin="0,20,0,0" Padding="15">
            <StackPanel>
                <TextBlock Text="✅ VISIBLE HOVER EXPANSION SCROLLBARS:" FontWeight="Bold" FontSize="16" Foreground="DarkBlue" Margin="0,0,0,10"/>
                
                <TextBlock Text="Fixed Visibility Issue:" FontWeight="Bold" Margin="0,0,0,5"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Increased default width from 2px to 6px (clearly visible)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Hover expansion from 6px to 8px (33% increase for better usability)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Faster animation: 200ms duration for more responsive feel"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Increased corner radius to 2px for better visual appeal"/>
                </StackPanel>
                
                <TextBlock Text="Updated Specifications:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Default width: 6px (visible and usable)" Margin="0,2"/>
                <TextBlock Text="• Hover width: 8px (enhanced usability)" Margin="0,2"/>
                <TextBlock Text="• Container width: 10px (accommodates expansion)" Margin="0,2"/>
                <TextBlock Text="• Margins: 2px on each side for centering" Margin="0,2"/>
                <TextBlock Text="• Corner radius: 2px (modern rounded appearance)" Margin="0,2"/>
                <TextBlock Text="• Animation duration: 200ms (responsive transitions)" Margin="0,2"/>
                
                <TextBlock Text="Visual States:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Default: 6px width, light gray (#999999)" Margin="0,2"/>
                <TextBlock Text="• Hover: 8px width, medium gray (#666666)" Margin="0,2"/>
                <TextBlock Text="• Dragging: 8px width, dark gray (#333333)" Margin="0,2"/>
                <TextBlock Text="• Smooth transitions between all states" Margin="0,2"/>
                
                <TextBlock Text="Hover Expansion Behavior:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Mouse enters scrollbar area → 6px to 8px expansion" Margin="0,2"/>
                <TextBlock Text="• Mouse leaves scrollbar area → 8px to 6px contraction" Margin="0,2"/>
                <TextBlock Text="• Entire scrollbar track responds to hover (not just thumb)" Margin="0,2"/>
                <TextBlock Text="• No layout shifting during expansion/contraction" Margin="0,2"/>
                
                <TextBlock Text="Benefits Achieved:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Clearly visible scrollbars in default state" Margin="0,2"/>
                <TextBlock Text="• Enhanced usability on hover without being intrusive" Margin="0,2"/>
                <TextBlock Text="• Smooth, polished animations for professional feel" Margin="0,2"/>
                <TextBlock Text="• Optimal balance between visibility and compactness" Margin="0,2"/>
                <TextBlock Text="• HandyControl-style interaction pattern" Margin="0,2"/>
                
                <TextBlock Text="Test Instructions:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Click the ListClock TimePicker dropdown button" Margin="0,2"/>
                <TextBlock Text="• You should now clearly see 6px scrollbars in hours/minutes columns" Margin="0,2"/>
                <TextBlock Text="• Move mouse over scrollbar areas to see smooth expansion to 8px" Margin="0,2"/>
                <TextBlock Text="• Move mouse away to see smooth contraction back to 6px" Margin="0,2"/>
                <TextBlock Text="• Test scrolling functionality - should work perfectly" Margin="0,2"/>
                <TextBlock Text="• Observe the rounded corners and smooth color transitions" Margin="0,2"/>
                
                <TextBlock Text="🎉 Scrollbars are now visible and have perfect hover expansion!" 
                           FontWeight="Bold" FontSize="14" Foreground="DarkGreen" Margin="0,15,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
