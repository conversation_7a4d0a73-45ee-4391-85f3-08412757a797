using System;
using System.Windows;

namespace StandaloneTimePickerTest
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();

            // Leave initial times empty (null) - no default time set

            // Subscribe to events
            BasicTimePicker.SelectedTimeChanged += (s, e) => UpdateTimeDisplay();
            ListClockTimePicker.SelectedTimeChanged += (s, e) => UpdateTimeDisplay();

            UpdateTimeDisplay();
        }

        private void GetTimes_Click(object sender, RoutedEventArgs e)
        {
            UpdateTimeDisplay();
        }

        private void UpdateTimeDisplay()
        {
            BasicTimeDisplay.Text = $"Basic TimePicker: {BasicTimePicker.SelectedTime?.ToString("HH:mm:ss") ?? "No time selected"}";
            ListClockTimeDisplay.Text = $"ListClock TimePicker: {ListClockTimePicker.SelectedTime?.ToString("HH:mm:ss") ?? "No time selected"}";
        }
    }
}
