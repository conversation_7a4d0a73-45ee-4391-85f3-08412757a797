using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using StandaloneTimePicker.Data;

namespace StandaloneTimePicker.Controls.Panels
{
    /// <summary>
    /// A panel that arranges child elements in a circular layout
    /// </summary>
    public class CirclePanel : Panel
    {
        /// <summary>
        /// Dependency property for the diameter of the circle
        /// </summary>
        public static readonly DependencyProperty DiameterProperty = DependencyProperty.Register(
            nameof(Diameter), typeof(double), typeof(CirclePanel), new FrameworkPropertyMetadata(170.0, FrameworkPropertyMetadataOptions.AffectsMeasure));

        /// <summary>
        /// Gets or sets the diameter of the circle
        /// </summary>
        public double Diameter
        {
            get => (double)GetValue(DiameterProperty);
            set => SetValue(DiameterProperty, value);
        }

        /// <summary>
        /// Dependency property for keeping elements vertical
        /// </summary>
        public static readonly DependencyProperty KeepVerticalProperty = DependencyProperty.Register(
            nameof(KeepVertical), typeof(bool), typeof(CirclePanel), new FrameworkPropertyMetadata(ValueBoxes.FalseBox, FrameworkPropertyMetadataOptions.AffectsMeasure));

        /// <summary>
        /// Gets or sets whether to keep child elements vertical (not rotated)
        /// </summary>
        public bool KeepVertical
        {
            get => (bool)GetValue(KeepVerticalProperty);
            set => SetValue(KeepVerticalProperty, ValueBoxes.BooleanBox(value));
        }

        /// <summary>
        /// Dependency property for the offset angle
        /// </summary>
        public static readonly DependencyProperty OffsetAngleProperty = DependencyProperty.Register(
            nameof(OffsetAngle), typeof(double), typeof(CirclePanel), new FrameworkPropertyMetadata(ValueBoxes.Double0Box, FrameworkPropertyMetadataOptions.AffectsMeasure));

        /// <summary>
        /// Gets or sets the offset angle for the first element
        /// </summary>
        public double OffsetAngle
        {
            get => (double)GetValue(OffsetAngleProperty);
            set => SetValue(OffsetAngleProperty, value);
        }

        /// <summary>
        /// Measures the size required for child elements
        /// </summary>
        /// <param name="availableSize">The available size</param>
        /// <returns>The desired size</returns>
        protected override Size MeasureOverride(Size availableSize)
        {
            var size = new Size(Diameter, Diameter);

            foreach (UIElement child in Children)
            {
                child.Measure(availableSize);
            }

            return size;
        }

        /// <summary>
        /// Arranges child elements in a circular layout
        /// </summary>
        /// <param name="finalSize">The final size for the panel</param>
        /// <returns>The actual size used</returns>
        protected override Size ArrangeOverride(Size finalSize)
        {
            var radius = Diameter / 2.0;
            var count = Children.Count;
            if (count == 0) return finalSize;

            var perDeg = 360.0 / count;
            var offsetAngle = OffsetAngle;
            var keepVertical = KeepVertical;
            var i = 0;

            foreach (UIElement element in Children)
            {
                var centerX = element.DesiredSize.Width / 2.0;
                var centerY = element.DesiredSize.Height / 2.0;
                var angle = perDeg * i++ + offsetAngle;

                var transform = new RotateTransform
                {
                    CenterX = centerX,
                    CenterY = centerY,
                    Angle = keepVertical ? 0 : angle
                };
                element.RenderTransform = transform;

                var r = Math.PI * angle / 180.0;
                var x = radius * Math.Cos(r);
                var y = radius * Math.Sin(r);

                var rectX = x + finalSize.Width / 2 - centerX;
                var rectY = y + finalSize.Height / 2 - centerY;

                element.Arrange(new Rect(rectX, rectY, element.DesiredSize.Width, element.DesiredSize.Height));
            }

            return finalSize;
        }
    }
}
