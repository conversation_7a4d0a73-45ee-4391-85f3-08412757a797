// TODO: Refactor.

.posts-expand {
  padding-top: 40px;
}

+mobile() {
  .posts-expand {
    margin: 0 20px;
  }

  .post-body {
    pre {
      .gutter pre {
        padding-right: 10px;
      }
    }

    .highlight {
      margin-left: 0px;
      margin-right: 0px;
      padding: 0;
      .gutter pre {
        padding-right: 10px;
      }
    }
  }
}

.posts-expand .post-body {
  +desktop() {
    text-align: unquote(hexo-config('text_align.desktop'));
  }
  +tablet-mobile() {
    text-align: unquote(hexo-config('text_align.mobile'));
  }

  h2, h3, h4, h5, h6 {
    padding-top: 10px;

    .header-anchor {
      float: right;
      margin-left: 10px;
      color: $grey-light;
      border-bottom-style: none;
      visibility: hidden;

      &:hover {
        color: inherit;
      }
    }

    &:hover .header-anchor {
      visibility: visible;
    }
  }

  img {
    box-sizing: border-box;
    margin: 0 auto 25px;
    padding: 3px;
    border: 1px solid $gray-lighter;
  }
}
