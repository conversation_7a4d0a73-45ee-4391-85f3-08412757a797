name: Build

on: [push, pull_request]

jobs:
  build:
    runs-on: windows-latest
    steps:
      - name: Checkout
        uses: actions/checkout@master

      - name: Setup .NET Core SDK
        uses: actions/setup-dotnet@master
        with:
          dotnet-version: '8.0.x'
          include-prerelease: true

      - name: Build
        working-directory: ./build
        run: |
            dotnet new tool-manifest
            dotnet tool install cake.tool --version 4.0.0
            dotnet tool install minver-cli --version 3.0.0
            dotnet cake
