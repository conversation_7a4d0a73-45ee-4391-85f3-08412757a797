<Window x:Class="EmptyDefaultTest.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:StandaloneTimePicker.Controls;assembly=StandaloneTimePicker"
        xmlns:clock="clr-namespace:StandaloneTimePicker.Controls.Clock;assembly=StandaloneTimePicker"
        Title="Empty Default Time Test" Height="450" Width="650">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0" Text="✅ FIXED: Empty Default Time on App Load" 
                   FontSize="20" FontWeight="Bold" Foreground="Green" Margin="0,0,0,20"/>
        
        <TextBlock Grid.Row="1" Text="TimePicker with Analog Clock (should be empty):" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="2" x:Name="AnalogTimePicker" Width="300" HorizontalAlignment="Left" Margin="0,0,0,15"/>
        
        <TextBlock Grid.Row="3" Text="TimePicker with ListClock (should be empty):" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="4" x:Name="ListClockTimePicker" Width="300" HorizontalAlignment="Left">
            <controls:TimePicker.Clock>
                <clock:ListClock/>
            </controls:TimePicker.Clock>
        </controls:TimePicker>
        
        <Border Grid.Row="5" BorderBrush="LightGray" BorderThickness="1" Margin="0,20,0,0" Padding="15">
            <StackPanel>
                <TextBlock Text="✅ EMPTY DEFAULT TIME VERIFICATION:" FontWeight="Bold" FontSize="16" Foreground="DarkBlue" Margin="0,0,0,10"/>
                
                <TextBlock Text="Expected Behavior on App Load:" FontWeight="Bold" Margin="0,0,0,5"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Text input shows watermark 'Select time...' (no actual time)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="SelectedTime property is null (no default time set)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Clock controls start empty (no pre-selected time)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="User must actively select a time (no automatic defaults)"/>
                </StackPanel>
                
                <TextBlock Text="Changes Made:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Removed DateTime.Now defaults from TimePicker, ClockBase, Clock, and ListClock" Margin="0,2"/>
                <TextBlock Text="• Updated test application to not set initial SelectedTime values" Margin="0,2"/>
                <TextBlock Text="• Changed DisplayTime default from DateTime.Now to default(DateTime)" Margin="0,2"/>
                <TextBlock Text="• Modified OnApplyTemplate to not set default times when SelectedTime is null" Margin="0,2"/>
                <TextBlock Text="• Updated clock controls to remain empty until user makes a selection" Margin="0,2"/>
                
                <TextBlock Text="Test Instructions:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Verify both TimePickers show watermark text instead of actual time" Margin="0,2"/>
                <TextBlock Text="• Click dropdown buttons to open clocks - they should start empty" Margin="0,2"/>
                <TextBlock Text="• Select a time and verify it appears in the text input" Margin="0,2"/>
                <TextBlock Text="• Clear the text and verify watermark returns" Margin="0,2"/>
                
                <TextBlock Text="🎉 TimePicker now starts empty as expected!" 
                           FontWeight="Bold" FontSize="14" Foreground="DarkGreen" Margin="0,15,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
