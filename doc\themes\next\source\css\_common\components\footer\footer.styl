.footer {
  font-size: 14px;
  color: $grey-dark;

  img { border: none; }
}

.footer-inner { text-align: center; }

@keyframes iconAnimate {
  0%, 100% { transform: scale(1); }
  10%, 30% { transform: scale(0.9); }
  20%, 40%, 60%, 80% { transform: scale(1.1); }
  50%, 70% { transform: scale(1.1); }
}

if hexo-config('footer.icon.animated') {
  #animate {
    animation: iconAnimate 1.33s ease-in-out infinite;
  }
}

.with-love {
  display: inline-block;
  margin: 0 5px;
  color: unquote(hexo-config('footer.icon.color'));
}

.powered-by,
.theme-info { display: inline-block; }
