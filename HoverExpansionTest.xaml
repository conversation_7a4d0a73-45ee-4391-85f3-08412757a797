<Window x:Class="HoverExpansionTest.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:StandaloneTimePicker.Controls;assembly=StandaloneTimePicker"
        xmlns:clock="clr-namespace:StandaloneTimePicker.Controls.Clock;assembly=StandaloneTimePicker"
        Title="Hover Expansion Scrollbar Test" Height="550" Width="750">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0" Text="✅ IMPLEMENTED: Hover Expansion Scrollbars" 
                   FontSize="20" FontWeight="Bold" Foreground="Green" Margin="0,0,0,20"/>
        
        <TextBlock Grid.Row="1" Text="TimePicker with ListClock (hover expansion scrollbars):" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="2" x:Name="ListClockTimePicker" Width="300" HorizontalAlignment="Left" Margin="0,0,0,15">
            <controls:TimePicker.Clock>
                <clock:ListClock/>
            </controls:TimePicker.Clock>
        </controls:TimePicker>
        
        <TextBlock Grid.Row="3" Text="Standard TimePicker with Analog Clock (for comparison):" FontWeight="Bold" Margin="0,0,0,5"/>
        <controls:TimePicker Grid.Row="4" x:Name="AnalogTimePicker" Width="300" HorizontalAlignment="Left"/>
        
        <Border Grid.Row="5" BorderBrush="LightGray" BorderThickness="1" Margin="0,20,0,0" Padding="15">
            <StackPanel>
                <TextBlock Text="✅ HOVER EXPANSION SCROLLBAR FEATURES:" FontWeight="Bold" FontSize="16" Foreground="DarkBlue" Margin="0,0,0,10"/>
                
                <TextBlock Text="Hover Expansion Implementation:" FontWeight="Bold" Margin="0,0,0,5"/>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Default width: 2px (ultra-thin, minimal visual presence)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Hover width: 4px (expanded for better usability)"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Smooth animation: 250ms duration for polished transitions"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Hover detection: Entire scrollbar area, not just thumb"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,3">
                    <TextBlock Text="✓" Foreground="Green" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="Layout preservation: No content shifting during expansion"/>
                </StackPanel>
                
                <TextBlock Text="Technical Implementation:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• DataTrigger with RelativeSource binding to detect hover on scrollbar area" Margin="0,2"/>
                <TextBlock Text="• DoubleAnimation targeting Border.Width property directly" Margin="0,2"/>
                <TextBlock Text="• EnterActions/ExitActions for smooth bidirectional animation" Margin="0,2"/>
                <TextBlock Text="• 4px fixed column width to accommodate expansion without layout shift" Margin="0,2"/>
                
                <TextBlock Text="Animation Details:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Enter Animation: 2px → 4px over 250ms" Margin="0,2"/>
                <TextBlock Text="• Exit Animation: 4px → 2px over 250ms" Margin="0,2"/>
                <TextBlock Text="• Easing: Default (smooth acceleration/deceleration)" Margin="0,2"/>
                <TextBlock Text="• Target: Border.Width property of thumb element" Margin="0,2"/>
                
                <TextBlock Text="Visual States:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Default: 2px width, ThirdlyTextBrush (#999999)" Margin="0,2"/>
                <TextBlock Text="• Hover: 4px width, SecondaryTextBrush (#666666)" Margin="0,2"/>
                <TextBlock Text="• Dragging: 4px width, PrimaryTextBrush (#333333)" Margin="0,2"/>
                <TextBlock Text="• Corner radius: 1px for subtle rounded appearance" Margin="0,2"/>
                
                <TextBlock Text="User Experience Benefits:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Minimal visual clutter when not in use (2px)" Margin="0,2"/>
                <TextBlock Text="• Better usability when needed (4px on hover)" Margin="0,2"/>
                <TextBlock Text="• Smooth, polished animations enhance perceived quality" Margin="0,2"/>
                <TextBlock Text="• HandyControl-style interaction pattern" Margin="0,2"/>
                <TextBlock Text="• No layout disruption during hover transitions" Margin="0,2"/>
                
                <TextBlock Text="Test Instructions:" FontWeight="Bold" Margin="0,15,0,5"/>
                <TextBlock Text="• Click the ListClock TimePicker dropdown button" Margin="0,2"/>
                <TextBlock Text="• Observe the ultra-thin 2px scrollbars in default state" Margin="0,2"/>
                <TextBlock Text="• Move mouse over the scrollbar area (not just the thumb)" Margin="0,2"/>
                <TextBlock Text="• Watch the smooth expansion animation to 4px width" Margin="0,2"/>
                <TextBlock Text="• Move mouse away and observe the smooth contraction back to 2px" Margin="0,2"/>
                <TextBlock Text="• Test both hours and minutes columns for consistent behavior" Margin="0,2"/>
                
                <TextBlock Text="🎉 HandyControl-style hover expansion is now working!" 
                           FontWeight="Bold" FontSize="14" Foreground="DarkGreen" Margin="0,15,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
