﻿++Solution 'HandyControl' ‎ (13 of 13 projects)
i:{00000000-0000-0000-0000-000000000000}:HandyControl.sln
++Solution Items
i:{00000000-0000-0000-0000-000000000000}:Solution Items
++Directory.Build.Props
i:{7f52cfd3-ef6c-4d3c-a9d4-b25bf726382a}:D:\Projects\HandyControl-master\src\Directory.Build.Props
++Net_GE45
i:{00000000-0000-0000-0000-000000000000}:Net_GE45
++HandyControlDemo_Net_GE45
i:{2f551c62-77f7-4fa5-8ad7-fcf53e92ae08}:HandyControlDemo_Net_GE45
++Dependencies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29504
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12150
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11818
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>7310
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>7308
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:>7278
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:>7277
++net45
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29547
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12272
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11876
++Assemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30540
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29613
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29913
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30595
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29661
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30045
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29868
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29709
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30018
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30568
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30248
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30270
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29731
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30178
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30154
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29761
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29782
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29587
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30220
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29842
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29683
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29635
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30425
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30403
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30199
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>29985
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30381
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30342
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30466
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30119
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30098
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30321
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>29558
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30077
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30302
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29822
++Microsoft.CSharp
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\microsoft.csharp.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.csharp
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\microsoft.csharp.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\microsoft.csharp.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.csharp
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\microsoft.csharp.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\microsoft.csharp.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\microsoft.csharp.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\microsoft.csharp.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\microsoft.csharp.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\microsoft.csharp.dll
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29825
++mscorlib
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\mscorlib.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\mscorlib.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\mscorlib.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\mscorlib.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\mscorlib.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\mscorlib.dll
++PresentationCore
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\presentationcore.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:presentationcore
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\presentationcore.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\presentationcore.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:presentationcore
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\presentationcore.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\presentationcore.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\presentationcore.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\presentationcore.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\presentationcore.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\presentationcore.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\presentationcore.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:presentationcore
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\presentationcore.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\presentationcore.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:presentationcore
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\presentationcore.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\presentationcore.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\presentationcore.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\presentationcore.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\presentationcore.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\presentationcore.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:presentationcore
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\presentationcore.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:presentationcore
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\presentationcore.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\presentationcore.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:presentationcore
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\presentationcore.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\presentationcore.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\presentationcore.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\presentationcore.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\presentationcore.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\presentationcore.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30317
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29838
++PresentationFramework
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\presentationframework.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:presentationframework
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\presentationframework.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\presentationframework.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:presentationframework
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\presentationframework.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\presentationframework.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\presentationframework.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\presentationframework.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\presentationframework.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\presentationframework.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\presentationframework.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:presentationframework
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\presentationframework.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\presentationframework.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:presentationframework
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\presentationframework.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\presentationframework.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\presentationframework.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\presentationframework.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\presentationframework.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\presentationframework.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:presentationframework
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\presentationframework.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:presentationframework
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\presentationframework.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\presentationframework.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:presentationframework
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\presentationframework.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\presentationframework.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\presentationframework.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\presentationframework.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\presentationframework.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\presentationframework.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30318
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29839
++System
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30315
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29836
++System.Core
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.core.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.core
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.core.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.core.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.core
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.core.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.core.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.core.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.core.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.core.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.core.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.core.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.core
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.core.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.core.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.core
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.core.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.core.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.core.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.core.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.core.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.core.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.core
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.core.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.core
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.core.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.core.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.core
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.core.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.core.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.core.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.core.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.core.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.core.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30314
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29835
++System.Data
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.data.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.data
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.data.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.data.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.data
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.data.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.data.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.data.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.data.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.data.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.data.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.data.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.data
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.data.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.data.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.data
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.data.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.data.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.data.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.data.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.data.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.data.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.data
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.data.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.data
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.data.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.data.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.data
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.data.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.data.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.data.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.data.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.data.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.data.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30309
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29830
++System.Drawing
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.drawing.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.drawing
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.drawing.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.drawing.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.drawing
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.drawing.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.drawing.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.drawing.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.drawing.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.drawing.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.drawing.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.drawing.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.drawing
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.drawing.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.drawing.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.drawing
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.drawing.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.drawing.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.drawing.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.drawing.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.drawing.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.drawing.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.drawing
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.drawing.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.drawing
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.drawing.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.drawing.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.drawing
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.drawing.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.drawing.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.drawing.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.drawing.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.drawing.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.drawing.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30303
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29823
++System.IO.Compression.FileSystem
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.io.compression.filesystem.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.io.compression.filesystem
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.io.compression.filesystem.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.io.compression.filesystem.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.io.compression.filesystem
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.io.compression.filesystem.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.io.compression.filesystem.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.io.compression.filesystem.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.io.compression.filesystem.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.io.compression.filesystem.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.io.compression.filesystem.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.io.compression.filesystem.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.io.compression.filesystem
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.io.compression.filesystem.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.io.compression.filesystem.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.io.compression.filesystem
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.io.compression.filesystem.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.io.compression.filesystem.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.io.compression.filesystem.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.io.compression.filesystem.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.io.compression.filesystem.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.io.compression.filesystem.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.io.compression.filesystem.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.io.compression.filesystem
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.io.compression.filesystem.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.io.compression.filesystem.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.io.compression.filesystem
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.io.compression.filesystem.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.io.compression.filesystem.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.io.compression.filesystem.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.io.compression.filesystem.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.io.compression.filesystem.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.io.compression.filesystem.dll
++System.Numerics
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.numerics.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.numerics
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.numerics.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.numerics.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.numerics
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.numerics.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.numerics.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.numerics.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.numerics.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.numerics.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.numerics.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.numerics.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.numerics
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.numerics.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.numerics.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.numerics
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.numerics.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.numerics.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.numerics.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.numerics.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.numerics.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.numerics.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.numerics
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.numerics.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.numerics
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.numerics.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.numerics.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.numerics
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.numerics.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.numerics.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.numerics.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.numerics.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.numerics.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.numerics.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30305
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29826
++System.Runtime.Serialization
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.runtime.serialization.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.runtime.serialization
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.runtime.serialization.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.runtime.serialization.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.runtime.serialization
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.runtime.serialization.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.runtime.serialization.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.runtime.serialization.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.runtime.serialization.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.runtime.serialization.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.runtime.serialization.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.runtime.serialization.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.runtime.serialization
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.runtime.serialization.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.runtime.serialization.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.runtime.serialization
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.runtime.serialization.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.runtime.serialization.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.runtime.serialization.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.runtime.serialization.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.runtime.serialization.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.runtime.serialization.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.runtime.serialization
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.runtime.serialization.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.runtime.serialization
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.runtime.serialization.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.runtime.serialization.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.runtime.serialization
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.runtime.serialization.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.runtime.serialization.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.runtime.serialization.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.runtime.serialization.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.runtime.serialization.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.runtime.serialization.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30311
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29832
++System.Windows.Controls.Ribbon
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.windows.controls.ribbon.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.windows.controls.ribbon
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.windows.controls.ribbon.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.windows.controls.ribbon.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.windows.controls.ribbon
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.windows.controls.ribbon.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.windows.controls.ribbon.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.windows.controls.ribbon.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.windows.controls.ribbon.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.windows.controls.ribbon.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.windows.controls.ribbon.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.windows.controls.ribbon.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.windows.controls.ribbon
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.windows.controls.ribbon.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.windows.controls.ribbon.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.windows.controls.ribbon
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.windows.controls.ribbon.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.windows.controls.ribbon.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.windows.controls.ribbon.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.windows.controls.ribbon.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.windows.controls.ribbon.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.windows.controls.ribbon.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.windows.controls.ribbon.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.windows.controls.ribbon
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.windows.controls.ribbon.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.windows.controls.ribbon.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.windows.controls.ribbon
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.windows.controls.ribbon.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.windows.controls.ribbon.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.windows.controls.ribbon.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.windows.controls.ribbon.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.windows.controls.ribbon.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.windows.controls.ribbon.dll
++System.Xaml
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.xaml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.xaml
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.xaml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.xaml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.xaml
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xaml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.xaml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.xaml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.xaml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.xaml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.xaml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.xaml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.xaml
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.xaml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.xaml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.xaml
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xaml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.xaml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.xaml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.xaml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.xaml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.xaml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.xaml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.xaml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.xaml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xaml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.xaml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.xaml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.xaml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.xaml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.xaml.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30307
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29828
++System.Xml
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.xml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.xml
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.xml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.xml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.xml
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.xml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.xml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.xml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.xml.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.xml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.xml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.xml
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.xml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.xml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.xml
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.xml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.xml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.xml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.xml.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.xml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.xml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.xml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.xml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.xml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.xml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.xml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.xml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.xml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.xml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.xml.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.xml.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30313
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29834
++System.Xml.Linq
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.xml.linq.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.xml.linq
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.xml.linq.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.xml.linq.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:system.xml.linq
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xml.linq.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.xml.linq.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.xml.linq.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.xml.linq.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.xml.linq.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.xml.linq.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.xml.linq.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.xml.linq
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.xml.linq.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.xml.linq.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:system.xml.linq
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xml.linq.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.xml.linq.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.xml.linq.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.xml.linq.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.xml.linq.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.xml.linq.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.xml.linq
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\system.xml.linq.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.xml.linq
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\system.xml.linq.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\system.xml.linq.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:system.xml.linq
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xml.linq.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\system.xml.linq.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\system.xml.linq.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\system.xml.linq.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\system.xml.linq.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\system.xml.linq.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30304
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29824
++UIAutomationClient
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\uiautomationclient.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:uiautomationclient
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\uiautomationclient.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\uiautomationclient.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:uiautomationclient
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\uiautomationclient.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\uiautomationclient.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\uiautomationclient.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\uiautomationclient.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\uiautomationclient.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\uiautomationclient.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\uiautomationclient.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:uiautomationclient
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\uiautomationclient.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\uiautomationclient.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:uiautomationclient
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\uiautomationclient.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\uiautomationclient.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\uiautomationclient.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\uiautomationclient.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\uiautomationclient.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\uiautomationclient.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:uiautomationclient
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\uiautomationclient.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:uiautomationclient
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\uiautomationclient.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\uiautomationclient.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:uiautomationclient
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\uiautomationclient.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\uiautomationclient.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\uiautomationclient.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\uiautomationclient.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\uiautomationclient.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\uiautomationclient.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30308
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29829
++UIAutomationClientsideProviders
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\uiautomationclientsideproviders.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:uiautomationclientsideproviders
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\uiautomationclientsideproviders.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\uiautomationclientsideproviders.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:uiautomationclientsideproviders
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\uiautomationclientsideproviders.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\uiautomationclientsideproviders.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\uiautomationclientsideproviders.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\uiautomationclientsideproviders.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\uiautomationclientsideproviders.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\uiautomationclientsideproviders.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\uiautomationclientsideproviders.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:uiautomationclientsideproviders
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\uiautomationclientsideproviders.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\uiautomationclientsideproviders.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:uiautomationclientsideproviders
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\uiautomationclientsideproviders.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\uiautomationclientsideproviders.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\uiautomationclientsideproviders.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\uiautomationclientsideproviders.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\uiautomationclientsideproviders.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\uiautomationclientsideproviders.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:uiautomationclientsideproviders
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\uiautomationclientsideproviders.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:uiautomationclientsideproviders
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\uiautomationclientsideproviders.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\uiautomationclientsideproviders.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:uiautomationclientsideproviders
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\uiautomationclientsideproviders.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\uiautomationclientsideproviders.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\uiautomationclientsideproviders.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\uiautomationclientsideproviders.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\uiautomationclientsideproviders.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\uiautomationclientsideproviders.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30312
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29833
++UIAutomationProvider
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\uiautomationprovider.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:uiautomationprovider
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\uiautomationprovider.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\uiautomationprovider.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:uiautomationprovider
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\uiautomationprovider.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\uiautomationprovider.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\uiautomationprovider.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\uiautomationprovider.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\uiautomationprovider.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\uiautomationprovider.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\uiautomationprovider.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:uiautomationprovider
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\uiautomationprovider.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\uiautomationprovider.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:uiautomationprovider
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\uiautomationprovider.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\uiautomationprovider.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\uiautomationprovider.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\uiautomationprovider.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\uiautomationprovider.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\uiautomationprovider.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:uiautomationprovider
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\uiautomationprovider.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:uiautomationprovider
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\uiautomationprovider.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\uiautomationprovider.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:uiautomationprovider
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\uiautomationprovider.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\uiautomationprovider.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\uiautomationprovider.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\uiautomationprovider.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\uiautomationprovider.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\uiautomationprovider.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30306
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29827
++UIAutomationTypes
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\uiautomationtypes.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:uiautomationtypes
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\uiautomationtypes.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\uiautomationtypes.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:uiautomationtypes
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\uiautomationtypes.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\uiautomationtypes.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\uiautomationtypes.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\uiautomationtypes.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\uiautomationtypes.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\uiautomationtypes.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\uiautomationtypes.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:uiautomationtypes
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\uiautomationtypes.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\uiautomationtypes.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:uiautomationtypes
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\uiautomationtypes.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\uiautomationtypes.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\uiautomationtypes.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\uiautomationtypes.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\uiautomationtypes.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\uiautomationtypes.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:uiautomationtypes
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\uiautomationtypes.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:uiautomationtypes
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\uiautomationtypes.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\uiautomationtypes.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:uiautomationtypes
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\uiautomationtypes.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\uiautomationtypes.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\uiautomationtypes.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\uiautomationtypes.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\uiautomationtypes.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\uiautomationtypes.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30310
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29831
++WindowsBase
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\windowsbase.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:windowsbase
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\windowsbase.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\windowsbase.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:windowsbase
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\windowsbase.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\windowsbase.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\windowsbase.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\windowsbase.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\windowsbase.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\windowsbase.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\windowsbase.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:windowsbase
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\windowsbase.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\windowsbase.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:windowsbase
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\windowsbase.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\windowsbase.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\windowsbase.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\windowsbase.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\windowsbase.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\windowsbase.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:windowsbase
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net45\1.0.3\build\.netframework\v4.5\windowsbase.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:windowsbase
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.5.2\windowsbase.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\users\<USER>\.nuget\packages\microsoft.netframework.referenceassemblies.net46\1.0.3\build\.netframework\v4.6\windowsbase.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:windowsbase
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\windowsbase.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7\windowsbase.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.1\windowsbase.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.7.2\windowsbase.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8\windowsbase.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.8.1\windowsbase.dll
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30316
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29837
++Packages
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30535
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29608
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29908
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30590
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29656
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30040
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29863
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29704
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30013
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30563
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30243
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30495
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29810
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29901
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29947
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30070
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30295
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30268
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29729
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30176
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30152
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29759
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29780
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29585
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30218
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29840
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29681
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29633
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30517
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30509
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30462
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30150
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29753
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30009
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30423
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30401
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30197
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>29983
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30379
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30340
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30464
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30117
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30096
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30319
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>29556
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30075
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>29583
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30449
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30377
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30531
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>29967
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30364
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30300
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29817
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:>29964
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:>29981
++Projects
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29548
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29524
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29521
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29542
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29515
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29518
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29545
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29509
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29512
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29539
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29506
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29554
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29551
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29536
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29533
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29530
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29527
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12273
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12188
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12182
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12251
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12170
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12176
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12267
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12158
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12164
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12227
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12152
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12285
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12279
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12221
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12206
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12200
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12194
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>7311
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>7309
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:>29969
++net451
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29523
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12187
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11866
++net452
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29520
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12181
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11865
++net46
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29541
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12237
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11873
++net461
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29514
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12169
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11863
++net462
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29517
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12175
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11864
++net47
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29544
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12266
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11874
++net471
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29508
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12157
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11842
++net472
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29511
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12163
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11862
++net48
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29538
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12226
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11871
++net481
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29505
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12151
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11841
++net5.0-windows
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29553
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12284
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11879
++net6.0-windows
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29550
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12278
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11878
++net7.0-windows
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29535
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12220
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11870
++net8.0-windows
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29532
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12205
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11869
++netcoreapp3.0
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29529
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12199
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11868
++netcoreapp3.1
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29526
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>12193
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11867
++Properties
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontroldemo_net_ge45\properties\
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\properties\
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\properties\
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\net_40\handycontroldemo_net_40\properties\
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\properties\
++Data
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontroldemo_net_ge45\data\
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\service\data\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\net_40\handycontroldemo_net_40\data\
++DemoInfo.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\demoinfo.json
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\demoinfo.json
++MessageToken.tt
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\messagetoken.tt
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\messagetoken.tt
++Resources
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontroldemo_net_ge45\resources\
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\resources\
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\resources\
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\net_40\handycontroldemo_net_40\resources\
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\resources\
++AvalonEdit (6.0.1)
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30074
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:avalonedit
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29821
++Microsoft.NETFramework.ReferenceAssemblies (1.0.3)
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30071
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netframework.referenceassemblies
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29754
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>29968
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netframework.referenceassemblies/1.0.3
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:>30301
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29819
++MvvmLightLibsStd10 (5.4.1.1)
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30072
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:mvvmlightlibsstd10
++Newtonsoft.Json (11.0.2)
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30073
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:newtonsoft.json
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29820
++HandyControl_Net_GE45
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net45\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net451\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net452\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net46\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net461\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net462\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net47\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net471\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net472\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net48\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net481\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net5.0-windows\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net6.0-windows\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net7.0-windows\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\net8.0-windows\handycontrol.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30065
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\bin\debug\netcoreapp3.1\handycontrol.dll
i:{2f551c62-77f7-4fa5-8ad7-fcf53e92ae08}:HandyControl_Net_GE45
++HandyControlDemo_Code
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net45\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net451\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net452\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net46\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net461\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net462\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net47\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net471\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net472\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net48\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net481\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net5.0-windows\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net6.0-windows\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net7.0-windows\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\net8.0-windows\handycontroldemocode.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30066
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\bin\debug\netcoreapp3.1\handycontroldemocode.dll
i:{0938270d-e9f0-489b-af9e-5c5ad7c69897}:HandyControlDemo_Code
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29815
++HandyControlDemo_Shared
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
i:{0938270d-e9f0-489b-af9e-5c5ad7c69897}:HandyControlDemo_Shared
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\handycontroldemo_shared.projitems
++Analyzers
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30489
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29803
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29890
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29935
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30511
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30502
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30451
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30138
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>29577
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30442
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30366
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30519
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:>29952
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:>29971
++Frameworks
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30492
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29807
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29898
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>29944
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30067
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30292
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30514
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30506
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30459
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30147
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29750
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>30006
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>29580
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30446
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30374
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30528
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>29958
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>30361
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:>29961
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:>29979
++Langs
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontroldemo_net_ge45\properties\langs\
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\properties\langs\
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\properties\langs\
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\net_40\handycontroldemo_net_40\properties\langs\
++App.config
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\app.config
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\app.config
++MessageToken.cs
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\messagetoken.cs
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\messagetoken.cs
++Img
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontroldemo_net_ge45\resources\img\
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\net_40\handycontroldemo_net_40\resources\img\
++xshd
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontroldemo_net_ge45\resources\xshd\
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\net_40\handycontroldemo_net_40\resources\xshd\
++fabric-icons.ttf
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\fabric-icons.ttf
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\fabric-icons.ttf
++Registry.txt
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\registry.txt
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\registry.txt
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\targets\..\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:c:\program files\dotnet\sdk\8.0.403\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.NETCore.App
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netcore.app
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netcore.app
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netcore.app
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netcore.app
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30069
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.netcore.app
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netcore.app
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netcore.app
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netcore.app
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netcore.app
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29752
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.netcore.app
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netcore.app
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netcore.app
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netcore.app
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netcore.app
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>29966
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.netcore.app
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:>29963
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:>29980
++Microsoft.WindowsDesktop.App.WPF
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.windowsdesktop.app.wpf
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.windowsdesktop.app.wpf
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.windowsdesktop.app.wpf
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.windowsdesktop.app.wpf
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:>30068
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:microsoft.windowsdesktop.app.wpf
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.windowsdesktop.app.wpf
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.windowsdesktop.app.wpf
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.windowsdesktop.app.wpf
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.windowsdesktop.app.wpf
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:>29751
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:microsoft.windowsdesktop.app.wpf
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.windowsdesktop.app.wpf
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.windowsdesktop.app.wpf
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.windowsdesktop.app.wpf
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.windowsdesktop.app.wpf
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>29962
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:microsoft.windowsdesktop.app.wpf
++System.Text.Json.SourceGeneration
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\6.0.35\analyzers/dotnet/cs/system.text.json.sourcegeneration.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/system.text.json.sourcegeneration.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/system.text.json.sourcegeneration.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\6.0.35\analyzers/dotnet/cs/system.text.json.sourcegeneration.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/system.text.json.sourcegeneration.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/system.text.json.sourcegeneration.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\6.0.35\analyzers/dotnet/cs/system.text.json.sourcegeneration.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/system.text.json.sourcegeneration.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/system.text.json.sourcegeneration.dll
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Windows.Forms.Analyzers
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\6.0.35\analyzers/dotnet/system.windows.forms.analyzers.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\7.0.20\analyzers/dotnet/system.windows.forms.analyzers.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\8.0.10\analyzers/dotnet/system.windows.forms.analyzers.dll
++System.Windows.Forms.Analyzers.CSharp
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\6.0.35\analyzers/dotnet/cs/system.windows.forms.analyzers.csharp.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\7.0.20\analyzers/dotnet/cs/system.windows.forms.analyzers.csharp.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\8.0.10\analyzers/dotnet/cs/system.windows.forms.analyzers.csharp.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/microsoft.interop.javascript.jsimportgenerator.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/microsoft.interop.javascript.jsimportgenerator.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/microsoft.interop.javascript.jsimportgenerator.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/microsoft.interop.javascript.jsimportgenerator.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/microsoft.interop.javascript.jsimportgenerator.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/microsoft.interop.javascript.jsimportgenerator.dll
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/microsoft.interop.libraryimportgenerator.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/microsoft.interop.libraryimportgenerator.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/microsoft.interop.libraryimportgenerator.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/microsoft.interop.libraryimportgenerator.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/microsoft.interop.libraryimportgenerator.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/microsoft.interop.libraryimportgenerator.dll
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/microsoft.interop.sourcegeneration.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/microsoft.interop.sourcegeneration.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/microsoft.interop.sourcegeneration.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/microsoft.interop.sourcegeneration.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/microsoft.interop.sourcegeneration.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/microsoft.interop.sourcegeneration.dll
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/system.text.regularexpressions.generator.dll
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/system.text.regularexpressions.generator.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/system.text.regularexpressions.generator.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/system.text.regularexpressions.generator.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers/dotnet/cs/system.text.regularexpressions.generator.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/system.text.regularexpressions.generator.dll
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\7.0.20\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/microsoft.interop.cominterfacegenerator.dll
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/microsoft.interop.cominterfacegenerator.dll
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.10\analyzers/dotnet/cs/microsoft.interop.cominterfacegenerator.dll
++Lang.ca-ES.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.ca-es.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.ca-es.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.ca-es.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.ca-es.resx
++Lang.cs.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.cs.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.cs.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.cs.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.cs.resx
++Lang.en.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.en.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.en.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.en.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.en.resx
++Lang.es.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.es.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.es.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.es.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.es.resx
++Lang.fa.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.fa.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.fa.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.fa.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.fa.resx
++Lang.fr.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.fr.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.fr.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.fr.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.fr.resx
++Lang.ja.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.ja.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.ja.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.ja.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.ja.resx
++Lang.ko-KR.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.ko-kr.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.ko-kr.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.ko-kr.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.ko-kr.resx
++Lang.pl.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.pl.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.pl.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.pl.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.pl.resx
++Lang.pt-BR.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.pt-br.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.pt-br.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.pt-br.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.pt-br.resx
++Lang.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.resx
++Lang.ru.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.ru.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.ru.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.ru.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.ru.resx
++Lang.tr.resx
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.tr.resx
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.tr.resx
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.tr.resx
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.tr.resx
++LangProvider.tt
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\langprovider.tt
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\langprovider.tt
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\langprovider.tt
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\langprovider.tt
++Album
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontroldemo_net_ge45\resources\img\album\
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\net_40\handycontroldemo_net_40\resources\img\album\
++Avatar
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontroldemo_net_ge45\resources\img\avatar\
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\net_40\handycontroldemo_net_40\resources\img\avatar\
++Chat
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontroldemo_net_ge45\resources\img\chat\
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\net_40\handycontroldemo_net_40\resources\img\chat\
++DevOps
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontroldemo_net_ge45\resources\img\devops\
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\net_40\handycontroldemo_net_40\resources\img\devops\
++Flag
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontroldemo_net_ge45\resources\img\flag\
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\net_40\handycontroldemo_net_40\resources\img\flag\
++QQ
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\net_ge45\handycontroldemo_net_ge45\resources\img\qq\
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\net_40\handycontroldemo_net_40\resources\img\qq\
++1.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\1.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\1.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\1.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\1.jpg
++2.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\2.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\2.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\2.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\2.jpg
++3.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\3.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\3.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\3.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\3.jpg
++4.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\4.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\4.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\4.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\4.jpg
++5.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\5.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\5.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\5.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\5.jpg
++b1.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\b1.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\b1.jpg
++b2.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\b2.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\b2.jpg
++car_chase.gif
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\car_chase.gif
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\car_chase.gif
++chrome_dragon.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\chrome_dragon.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\chrome_dragon.png
++cloud.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\cloud.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\cloud.png
++Cover.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\cover.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\cover.png
++Dance.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\dance.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\dance.png
++icon.ico
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\icon.ico
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\icon.ico
++icon-white.ico
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\icon-white.ico
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\icon-white.ico
++Slack.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\slack.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\slack.png
++under_construction.gif
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\under_construction.gif
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\under_construction.gif
++CSharp-Dark.xshd
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\xshd\csharp-dark.xshd
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\xshd\csharp-dark.xshd
++XML-Dark.xshd
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\xshd\xml-dark.xshd
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\xshd\xml-dark.xshd
++Lang.Designer.cs
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.designer.cs
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.designer.cs
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\lang.designer.cs
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\lang.designer.cs
++LangProvider.cs
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\langprovider.cs
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\langprovider.cs
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\langs\langprovider.cs
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\properties\langs\langprovider.cs
++10.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\10.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\10.jpg
++6.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\6.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\6.jpg
++7.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\7.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\7.jpg
++8.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\8.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\8.jpg
++9.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\9.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\album\9.jpg
++avatar1.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\avatar\avatar1.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\avatar\avatar1.png
++avatar2.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\avatar\avatar2.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\avatar\avatar2.png
++avatar3.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\avatar\avatar3.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\avatar\avatar3.png
++avatar4.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\avatar\avatar4.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\avatar\avatar4.png
++avatar5.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\avatar\avatar5.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\avatar\avatar5.png
++avatar6.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\avatar\avatar6.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\avatar\avatar6.png
++chat_back1.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\chat\chat_back1.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\chat\chat_back1.jpg
++chat_back2.jpg
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\chat\chat_back2.jpg
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\chat\chat_back2.jpg
++DevOps-Boards.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\devops\devops-boards.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\devops\devops-boards.png
++DevOps-Overview.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\devops\devops-overview.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\devops\devops-overview.png
++DevOps-Pipelines.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\devops\devops-pipelines.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\devops\devops-pipelines.png
++DevOps-Repos.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\devops\devops-repos.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\devops\devops-repos.png
++DevOps-TestPlans.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\devops\devops-testplans.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\devops\devops-testplans.png
++ca-Es.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\ca-es.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\ca-es.png
++cn.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\cn.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\cn.png
++cs.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\cs.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\cs.png
++en.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\en.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\en.png
++es.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\es.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\es.png
++fa.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\fa.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\fa.png
++fr.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\fr.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\fr.png
++ja.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\ja.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\ja.png
++ko-KR.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\ko-kr.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\ko-kr.png
++pl.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\pl.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\pl.png
++pt-BR.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\pt-br.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\pt-br.png
++ru.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\ru.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\ru.png
++tr.png
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\tr.png
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\flag\tr.png
++1.gif
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\1.gif
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\1.gif
++10.gif
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\10.gif
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\10.gif
++2.gif
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\2.gif
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\2.gif
++3.gif
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\3.gif
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\3.gif
++4.gif
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\4.gif
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\4.gif
++5.gif
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\5.gif
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\5.gif
++6.gif
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\6.gif
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\6.gif
++7.gif
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\7.gif
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\7.gif
++8.gif
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\8.gif
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\8.gif
++9.gif
i:{9a7b52e6-94bb-4e7e-bb6a-7cf6761b79a7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\9.gif
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\img\qq\9.gif
++AssemblyInfo.cs
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\assemblyinfo.cs
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\properties\assemblyinfo.cs
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\properties\assemblyinfo.cs
++Effects
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\resources\effects\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\effects\
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\resources\effects\
++Images
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\resources\images\
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\resources\images\
++dropper.cur
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\dropper.cur
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\dropper.cur
++BrightnessEffect.ps
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\brightnesseffect.ps
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\brightnesseffect.ps
++ColorComplementEffect.ps
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\colorcomplementeffect.ps
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\colorcomplementeffect.ps
++ColorMatrixEffect.ps
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\colormatrixeffect.ps
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\colormatrixeffect.ps
++ContrastEffect.ps
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\contrasteffect.ps
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\contrasteffect.ps
++GrayScaleEffect.ps
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\grayscaleeffect.ps
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\grayscaleeffect.ps
++GlowWindow
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\net_ge45\handycontrol_net_ge45\resources\images\glowwindow\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\glowwindow\
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\resources\images\glowwindow\
++bottom.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\bottom.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\bottom.png
++bottomleft.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\bottomleft.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\bottomleft.png
++bottomright.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\bottomright.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\bottomright.png
++cornerbottomleft.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\cornerbottomleft.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\cornerbottomleft.png
++cornerbottomright.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\cornerbottomright.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\cornerbottomright.png
++cornertopleft.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\cornertopleft.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\cornertopleft.png
++cornertopright.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\cornertopright.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\cornertopright.png
++left.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\left.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\left.png
++leftbottom.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\leftbottom.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\leftbottom.png
++lefttop.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\lefttop.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\lefttop.png
++right.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\right.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\right.png
++rightbottom.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\rightbottom.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\rightbottom.png
++righttop.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\righttop.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\righttop.png
++top.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\top.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\top.png
++topleft.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\topleft.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\topleft.png
++topright.png
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\topright.png
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\images\glowwindow\topright.png
++HandyControl_Shared
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
i:{0938270d-e9f0-489b-af9e-5c5ad7c69897}:HandyControl_Shared
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\handycontrol_shared.projitems
++Microsoft.Expression.Drawing
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
i:{0938270d-e9f0-489b-af9e-5c5ad7c69897}:Microsoft.Expression.Drawing
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\microsoft.expression.drawing.projitems
++Microsoft.Expression.Interactions
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
i:{0938270d-e9f0-489b-af9e-5c5ad7c69897}:Microsoft.Expression.Interactions
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\microsoft.expression.interactions.projitems
++System.Windows.Interactivity
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{dc966e3d-bcff-4652-98cf-070e5494749a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
i:{0938270d-e9f0-489b-af9e-5c5ad7c69897}:System.Windows.Interactivity
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\system.windows.interactivity.projitems
++Shared
i:{00000000-0000-0000-0000-000000000000}:Shared
++AttachableCollection`1.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\attachablecollection`1.cs
++Behavior.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\behavior.cs
++Behavior`1.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\behavior`1.cs
++BehaviorCollection.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\behaviorcollection.cs
++DefaultTriggerAttribute.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\defaulttriggerattribute.cs
++EventTrigger.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\eventtrigger.cs
++EventTriggerBase.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\eventtriggerbase.cs
++EventTriggerBase`1.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\eventtriggerbase`1.cs
++IAttachedObject.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\iattachedobject.cs
++Interaction.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\interaction.cs
++InvokeCommandAction.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\invokecommandaction.cs
++NameResolvedEventArgs.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\nameresolvedeventargs.cs
++NameResolver.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\nameresolver.cs
++PreviewInvokeEventArgs.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\previewinvokeeventargs.cs
++TriggerAction.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\triggeraction.cs
++TriggerAction`1.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\triggeraction`1.cs
++TriggerActionCollection.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\triggeractioncollection.cs
++TriggerBase.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\triggerbase.cs
++TriggerCollection.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\triggercollection.cs
++TypeConstraintAttribute.cs
i:{6f0c9cff-2269-46a7-9664-478354c582a4}:d:\projects\handycontrol-master\src\shared\system.windows.interactivity\typeconstraintattribute.cs
++AdornerContainer.cs
i:{3531857c-28fc-4e0f-82d9-bfbb70740e04}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\adornercontainer.cs
++ExceptionStringTable.cs
i:{3531857c-28fc-4e0f-82d9-bfbb70740e04}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\exceptionstringtable.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\exceptionstringtable.cs
++ExtendedVisualStateManager.cs
i:{3531857c-28fc-4e0f-82d9-bfbb70740e04}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\extendedvisualstatemanager.cs
++FluidMoveBehavior.cs
i:{3531857c-28fc-4e0f-82d9-bfbb70740e04}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\fluidmovebehavior.cs
++FluidMoveBehaviorBase.cs
i:{3531857c-28fc-4e0f-82d9-bfbb70740e04}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\fluidmovebehaviorbase.cs
++FluidMoveScope.cs
i:{3531857c-28fc-4e0f-82d9-bfbb70740e04}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\fluidmovescope.cs
++MouseDragElementBehavior.cs
i:{3531857c-28fc-4e0f-82d9-bfbb70740e04}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\mousedragelementbehavior.cs
++TagType.cs
i:{3531857c-28fc-4e0f-82d9-bfbb70740e04}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\tagtype.cs
++TransitionEffect.cs
i:{3531857c-28fc-4e0f-82d9-bfbb70740e04}:d:\projects\handycontrol-master\src\shared\microsoft.expression.interactions\transitioneffect.cs
++Microsoft.Windows.Shell
i:{0938270d-e9f0-489b-af9e-5c5ad7c69897}:Microsoft.Windows.Shell
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\microsoft.windows.shell.projitems
++Standard
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\
++JumpItem.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\jumpitem.cs
++JumpItemRejectionReason.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\jumpitemrejectionreason.cs
++JumpItemsRejectedEventArgs.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\jumpitemsrejectedeventargs.cs
++JumpItemsRemovedEventArgs.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\jumpitemsremovedeventargs.cs
++JumpList.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\jumplist.cs
++JumpPath.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\jumppath.cs
++JumpTask.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\jumptask.cs
++SystemCommands.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\systemcommands.cs
++SystemParameters2.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\systemparameters2.cs
++TaskbarItemInfo.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\taskbariteminfo.cs
++TaskbarItemProgressState.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\taskbaritemprogressstate.cs
++ThumbButtonInfo.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\thumbbuttoninfo.cs
++ThumbButtonInfoCollection.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\thumbbuttoninfocollection.cs
++WindowChrome.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\windowchrome.cs
++WindowChromeWorker.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\windowchromeworker.cs
++AC.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ac.cs
++APPDOCLISTTYPE.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\appdoclisttype.cs
++Assert.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\assert.cs
++BI.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\bi.cs
++BITMAPINFO.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\bitmapinfo.cs
++BITMAPINFOHEADER.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\bitmapinfoheader.cs
++BLENDFUNCTION.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\blendfunction.cs
++CHANGEFILTERSTRUCT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\changefilterstruct.cs
++CLSID.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\clsid.cs
++CombineRgnResult.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\combinergnresult.cs
++CREATESTRUCT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\createstruct.cs
++CS.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\cs.cs
++DeviceCap.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\devicecap.cs
++DOGIF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\dogif.cs
++DoubleUtilities.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\doubleutilities.cs
++DpiHelper.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\dpihelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\dpihelper.cs
++DWM_SIT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\dwm_sit.cs
++DWM_TIMING_INFO.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\dwm_timing_info.cs
++DWMFLIP3D.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\dwmflip3d.cs
++DWMNCRP.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\dwmncrp.cs
++DWMWA.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\dwmwa.cs
++ErrorModes.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\errormodes.cs
++Facility.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\facility.cs
++FO.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\fo.cs
++FOF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\fof.cs
++GCLP.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\gclp.cs
++GPS.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\gps.cs
++GWL.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\gwl.cs
++HCF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\hcf.cs
++HIGHCONTRAST.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\highcontrast.cs
++HRESULT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\hresult.cs
++HT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ht.cs
++IApplicationDestinations.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\iapplicationdestinations.cs
++IApplicationDocumentLists.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\iapplicationdocumentlists.cs
++ICustomDestinationList.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\icustomdestinationlist.cs
++IEnumIDList.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ienumidlist.cs
++IEnumObjects.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ienumobjects.cs
++IID.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\iid.cs
++INPUT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\input.cs
++INPUT_TYPE.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\input_type.cs
++IObjectArray.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\iobjectarray.cs
++IObjectCollection.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\iobjectcollection.cs
++IObjectWithAppUserModelId.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\iobjectwithappusermodelid.cs
++IObjectWithProgId.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\iobjectwithprogid.cs
++IPropertyStore.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ipropertystore.cs
++IShellFolder.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ishellfolder.cs
++IShellItem.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ishellitem.cs
++IShellItem2.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ishellitem2.cs
++IShellItemArray.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ishellitemarray.cs
++IShellLinkW.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ishelllinkw.cs
++ITaskbarList.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\itaskbarlist.cs
++ITaskbarList2.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\itaskbarlist2.cs
++ITaskbarList3.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\itaskbarlist3.cs
++ITaskbarList4.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\itaskbarlist4.cs
++KDC.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\kdc.cs
++LOGFONT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\logfont.cs
++ManagedIStream.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\managedistream.cs
++MARGINS.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\margins.cs
++MessageHandler.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\messagehandler.cs
++MessageWindow.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\messagewindow.cs
++MF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\mf.cs
++MINMAXINFO.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\minmaxinfo.cs
++MONITORINFO.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\monitorinfo.cs
++MOUSEEVENTF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\mouseeventf.cs
++MOUSEINPUT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\mouseinput.cs
++MSGFLT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\msgflt.cs
++MSGFLTINFO.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\msgfltinfo.cs
++NativeMethods.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\nativemethods.cs
++NIF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\nif.cs
++NIIF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\niif.cs
++NIM.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\nim.cs
++NONCLIENTMETRICS.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\nonclientmetrics.cs
++NOTIFYICONDATA.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\notifyicondata.cs
++OLECMDEXECOPT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\olecmdexecopt.cs
++OLECMDF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\olecmdf.cs
++OLECMDID.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\olecmdid.cs
++PKEY.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\pkey.cs
++POINT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\point.cs
++PROPVARIANT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\propvariant.cs
++READYSTATE.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\readystate.cs
++RECT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\rect.cs
++RefPOINT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\refpoint.cs
++RefRECT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\refrect.cs
++RGBQUAD.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\rgbquad.cs
++RGN.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\rgn.cs
++SafeConnectionPointCookie.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\safeconnectionpointcookie.cs
++SafeDC.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\safedc.cs
++SafeFindHandle.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\safefindhandle.cs
++SafeGdiplusStartupToken.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\safegdiplusstartuptoken.cs
++SafeHBITMAP.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\safehbitmap.cs
++SC.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\sc.cs
++SFGAO.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\sfgao.cs
++SHARD.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\shard.cs
++SHARDAPPIDINFO.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\shardappidinfo.cs
++SHARDAPPIDINFOIDLIST.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\shardappidinfoidlist.cs
++SHARDAPPIDINFOLINK.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\shardappidinfolink.cs
++SHCONTF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\shcontf.cs
++SHFILEOPSTRUCT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\shfileopstruct.cs
++SHGDN.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\shgdn.cs
++SIATTRIBFLAGS.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\siattribflags.cs
++SICHINT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\sichint.cs
++SIGDN.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\sigdn.cs
++SIZE.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\size.cs
++SLGP.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\slgp.cs
++SM.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\sm.cs
++SPI.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\spi.cs
++SPIF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\spif.cs
++StartupInput.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\startupinput.cs
++StartupOutput.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\startupoutput.cs
++STATE_SYSTEM.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\state_system.cs
++Status.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\status.cs
++StockObject.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\stockobject.cs
++STPF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\stpf.cs
++STR_GPS.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\str_gps.cs
++SW.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\sw.cs
++SWP.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\swp.cs
++TBPF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\tbpf.cs
++THB.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\thb.cs
++THBF.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\thbf.cs
++THUMBBUTTON.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\thumbbutton.cs
++TITLEBARINFO.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\titlebarinfo.cs
++TITLEBARINFOEX.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\titlebarinfoex.cs
++ULW.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ulw.cs
++UNSIGNED_RATIO.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\unsigned_ratio.cs
++Utility.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\utility.cs
++Verify.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\verify.cs
++WIN32_FIND_DATAW.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\win32_find_dataw.cs
++Win32Error.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\win32error.cs
++Win32Value.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\win32value.cs
++WINDOWPLACEMENT.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\windowplacement.cs
++WINDOWPOS.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\windowpos.cs
++WINDOWTHEMEATTRIBUTETYPE.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\windowthemeattributetype.cs
++WM.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\wm.cs
++WNDCLASSEX.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\wndclassex.cs
++WndProc.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\wndproc.cs
++WndProcHook.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\wndprochook.cs
++WS.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ws.cs
++WS_EX.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\ws_ex.cs
++WTA_OPTIONS.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\wta_options.cs
++WTNCA.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\wtnca.cs
++WVR.cs
i:{bbe51380-8b21-49ff-9cfc-b29447a40999}:d:\projects\handycontrol-master\src\shared\microsoft.windows.shell\standard\wvr.cs
++Drawing
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\
++Media
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\
++Shapes
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\shapes\
++BezierCurveFlattener.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\beziercurveflattener.cs
++CommonExtensions.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\commonextensions.cs
++GeometryHelper.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\geometryhelper.cs
++MarchLocation.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\marchlocation.cs
++MarchStopReason.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\marchstopreason.cs
++MathHelper.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\mathhelper.cs
++PathFigureHelper.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\pathfigurehelper.cs
++PathGeometryHelper.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\pathgeometryhelper.cs
++PathSegmentData.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\pathsegmentdata.cs
++PathSegmentHelper.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\pathsegmenthelper.cs
++PolylineData.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\polylinedata.cs
++PolylineHelper.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\polylinehelper.cs
++RandomEngine.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\randomengine.cs
++SimpleSegment.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\drawing\simplesegment.cs
++ArcGeometrySource.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\arcgeometrysource.cs
++DrawingPropertyChangedEventArgs.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\drawingpropertychangedeventargs.cs
++DrawingPropertyMetadata.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\drawingpropertymetadata.cs
++DrawingPropertyMetadataOptions.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\drawingpropertymetadataoptions.cs
++GeometryEffect.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\geometryeffect.cs
++GeometryEffectConverter.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\geometryeffectconverter.cs
++GeometrySource`1.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\geometrysource`1.cs
++IArcGeometrySourceParameters.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\iarcgeometrysourceparameters.cs
++IGeometrySource.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\igeometrysource.cs
++IGeometrySourceExtensions.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\igeometrysourceextensions.cs
++IGeometrySourceParameters.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\igeometrysourceparameters.cs
++InvalidateGeometryReasons.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\invalidategeometryreasons.cs
++IShape.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\ishape.cs
++SketchGeometryEffect.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\sketchgeometryeffect.cs
++UnitType.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\media\unittype.cs
++Arc.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\shapes\arc.cs
++PrimitiveShape.cs
i:{e5dc7c25-6293-4660-b5fb-186496386b90}:d:\projects\handycontrol-master\src\shared\microsoft.expression.drawing\shapes\primitiveshape.cs
++Themes
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\themes\
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\resources\themes\
++Basic
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\basic\
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\basic\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\usercontrol\basic\
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\basic\
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\resources\themes\basic\
++Colors
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\basic\colors\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\colors\
++Basic.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\basic\basic.xaml
++Brushes.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\basic\brushes.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\brushes.xaml
++Converters.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\basic\converters.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\converters.xaml
++Fonts.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\basic\fonts.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\fonts.xaml
++Geometries.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\basic\geometries.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\geometries.xaml
++Styles
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\styles\
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\usercontrol\styles\
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\themes\styles\
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\styles\
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\views\styles\
++SkinDark.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\skindark.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\skindark.xaml
++SkinDefault.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\skindefault.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\skindefault.xaml
++Theme.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\theme.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\theme.xaml
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\themes\theme.xaml
++DemoTheme.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\demotheme.cs
++Service
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\service\
++Tools
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\usercontrol\tools\
++Converter
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\converter\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\
++Extension
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\extension\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\extension\
++Helper
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\helper\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\
++ValidationRule
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\validationrule\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\validationrule\
++NumericUpDownDemoRule.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\validationrule\numericupdowndemorule.cs
++HighlightingProvider.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\highlightingprovider.cs
++UserControl
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\usercontrol\
++Controls
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\usercontrol\controls\
++AnimationPathDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\animationpathdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\animationpathdemoctl.xaml
++AutoCompleteTextBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\autocompletetextboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\autocompletetextboxdemoctl.xaml
++BadgeDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\badgedemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\badgedemoctl.xaml
++ButtonGroupDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\buttongroupdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\buttongroupdemoctl.xaml
++CalendarWithClockDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\calendarwithclockdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\calendarwithclockdemoctl.xaml
++CardDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\carddemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\carddemoctl.xaml
++CarouselDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\carouseldemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\carouseldemoctl.xaml
++ChatBubbleDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\chatbubbledemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\chatbubbledemoctl.xaml
++CheckComboBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\checkcomboboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\checkcomboboxdemoctl.xaml
++CirclePanelDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\circlepaneldemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\circlepaneldemoctl.xaml
++ClockDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\clockdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\clockdemoctl.xaml
++ColorPickerDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\colorpickerdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\colorpickerdemoctl.xaml
++ComboBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\comboboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\comboboxdemoctl.xaml
++CompareSliderDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\comparesliderdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\comparesliderdemoctl.xaml
++CoverFlowDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\coverflowdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\coverflowdemoctl.xaml
++CoverViewDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\coverviewdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\coverviewdemoctl.xaml
++DatePickerDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\datepickerdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\datepickerdemoctl.xaml
++DateTimePickerDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\datetimepickerdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\datetimepickerdemoctl.xaml
++DateTimePickerDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\datetimepickerdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\datetimepickerdemoctl.xaml.cs
++DialogDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\dialogdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\dialogdemoctl.xaml
++DividerDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\dividerdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\dividerdemoctl.xaml
++DrawerDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\drawerdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\drawerdemoctl.xaml
++ElementGroupDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\elementgroupdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\elementgroupdemoctl.xaml
++FlexPanelDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\flexpaneldemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\flexpaneldemoctl.xaml
++FlipClockDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\flipclockdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\flipclockdemoctl.xaml
++FloatingBlockDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\floatingblockdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\floatingblockdemoctl.xaml
++GifImageDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\gifimagedemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\gifimagedemoctl.xaml
++GotoTopDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\gototopdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\gototopdemoctl.xaml
++GravatarDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\gravatardemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\gravatardemoctl.xaml
++GridDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\griddemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\griddemoctl.xaml
++GrowlDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\growldemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\growldemoctl.xaml
++HoneycombPanelDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\honeycombpaneldemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\honeycombpaneldemoctl.xaml
++ImageBlockDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\imageblockdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\imageblockdemoctl.xaml
++ImageBrowserDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\imagebrowserdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\imagebrowserdemoctl.xaml
++ImageSelectorDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\imageselectordemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\imageselectordemoctl.xaml
++LoadingDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\loadingdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\loadingdemoctl.xaml
++MagnifierDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\magnifierdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\magnifierdemoctl.xaml
++NotificationDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\notificationdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\notificationdemoctl.xaml
++NotifyIconDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\notifyicondemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\notifyicondemoctl.xaml
++NumericUpDownDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\numericupdowndemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\numericupdowndemoctl.xaml
++NumericUpDownDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\numericupdowndemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\numericupdowndemoctl.xaml.cs
++OutlineTextDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\outlinetextdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\outlinetextdemoctl.xaml
++PaginationDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\paginationdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\paginationdemoctl.xaml
++PasswordBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\passwordboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\passwordboxdemoctl.xaml
++PinBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\pinboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\pinboxdemoctl.xaml
++PoptipDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\poptipdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\poptipdemoctl.xaml
++PreviewSliderDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\previewsliderdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\previewsliderdemoctl.xaml
++ProgressBarDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\progressbardemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\progressbardemoctl.xaml
++ProgressButtonDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\progressbuttondemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\progressbuttondemoctl.xaml
++PropertyGridDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\propertygriddemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\propertygriddemoctl.xaml
++RangeSliderDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\rangesliderdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\rangesliderdemoctl.xaml
++RateDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\ratedemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\ratedemoctl.xaml
++RelativePanelDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\relativepaneldemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\relativepaneldemoctl.xaml
++RunningBlockDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\runningblockdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\runningblockdemoctl.xaml
++ScreenshotDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\screenshotdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\screenshotdemoctl.xaml
++ScrollViewerDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\scrollviewerdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\scrollviewerdemoctl.xaml
++SearchBarDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\searchbardemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\searchbardemoctl.xaml
++ShieldDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\shielddemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\shielddemoctl.xaml
++SideMenuDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\sidemenudemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\sidemenudemoctl.xaml
++SplitButtonDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\splitbuttondemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\splitbuttondemoctl.xaml
++SpriteDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\spritedemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\spritedemoctl.xaml
++StepBarDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\stepbardemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\stepbardemoctl.xaml
++TabControlDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\tabcontroldemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\tabcontroldemoctl.xaml
++TagDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\tagdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\tagdemoctl.xaml
++TextBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\textboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\textboxdemoctl.xaml
++TimeBarDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\timebardemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\timebardemoctl.xaml
++TimePickerDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\timepickerdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\timepickerdemoctl.xaml
++TimePickerDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\timepickerdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\timepickerdemoctl.xaml.cs
++TransferDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\transferdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\transferdemoctl.xaml
++TransitioningContentControlDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\transitioningcontentcontroldemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\transitioningcontentcontroldemoctl.xaml
++UniformSpacingPanelDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\uniformspacingpaneldemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\uniformspacingpaneldemoctl.xaml
++WaterfallPanelDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\waterfallpaneldemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\waterfallpaneldemoctl.xaml
++WatermarkDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\watermarkdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\watermarkdemoctl.xaml
++WindowDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\windowdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\windowdemoctl.xaml
++Main
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\main\
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\views\main\
++Practical
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\practical\
++ViewModel
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\viewmodel\
++ChatBoxViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\basic\chatboxviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\basic\chatboxviewmodel.cs
++InteractiveDialogViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\basic\interactivedialogviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\basic\interactivedialogviewmodel.cs
++Common
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\common\
++AutoCompleteTextBoxDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\autocompletetextboxdemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\autocompletetextboxdemoviewmodel.cs
++BadgeDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\badgedemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\badgedemoviewmodel.cs
++CardDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\carddemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\carddemoviewmodel.cs
++CoverViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\coverviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\coverviewmodel.cs
++DialogDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\dialogdemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\dialogdemoviewmodel.cs
++GrowlDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\growldemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\growldemoviewmodel.cs
++ImageBrowserDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\imagebrowserdemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\imagebrowserdemoviewmodel.cs
++NotificationDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\notificationdemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\notificationdemoviewmodel.cs
++NotifyIconDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\notifyicondemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\notifyicondemoviewmodel.cs
++PaginationDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\paginationdemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\paginationdemoviewmodel.cs
++SideMenuDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\sidemenudemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\sidemenudemoviewmodel.cs
++SplitButtonDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\splitbuttondemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\splitbuttondemoviewmodel.cs
++SpriteDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\spritedemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\spritedemoviewmodel.cs
++StepBarDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\stepbardemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\stepbardemoviewmodel.cs
++TabControlDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\tabcontroldemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\tabcontroldemoviewmodel.cs
++TagDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\tagdemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\tagdemoviewmodel.cs
++WindowDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\windowdemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\controls\windowdemoviewmodel.cs
++DemoViewModelBase`1.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\demoviewmodelbase`1.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\demoviewmodelbase`1.cs
++ViewModelLocator.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\viewmodellocator.cs
++Window
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\window\
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\window\
++app.manifest
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\app.manifest
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\app.manifest
++App.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\app.xaml
++MainWindow.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\mainwindow.xaml
++Enum
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\enum\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\
++Model
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\model\
++AppConfig.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\appconfig.cs
++GlobalData.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\globaldata.cs
++Colors.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\basic\colors\colors.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\colors\colors.xaml
++ColorsDark.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\basic\colors\colorsdark.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\colors\colorsdark.xaml
++Style.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\resources\themes\styles\style.xaml
++HatchBrushConverter.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\converter\hatchbrushconverter.cs
++String2BrushConverter.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\converter\string2brushconverter.cs
++StringRepeatConverter.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\converter\stringrepeatconverter.cs
++LangExtension.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\extension\langextension.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\extension\langextension.cs
++AssemblyHelper.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\helper\assemblyhelper.cs
++VersionHelper.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\helper\versionhelper.cs
++Win32Helper.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\tools\helper\win32helper.cs
++Avatar.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\avatar.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\avatar.xaml
++ChatBox.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\chatbox.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\chatbox.xaml
++GeometryItem.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\geometryitem.xaml
++IFull.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\ifull.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\ifull.cs
++InteractiveDialog.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\interactivedialog.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\interactivedialog.xaml
++TextDialog.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\textdialog.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\textdialog.xaml
++TextDialogWithTimer.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\textdialogwithtimer.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\textdialogwithtimer.xaml
++AnimationPathDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\animationpathdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\animationpathdemoctl.xaml.cs
++AutoCompleteTextBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\autocompletetextboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\autocompletetextboxdemoctl.xaml.cs
++BadgeDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\badgedemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\badgedemoctl.xaml.cs
++ButtonGroupDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\buttongroupdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\buttongroupdemoctl.xaml.cs
++CalendarWithClockDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\calendarwithclockdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\calendarwithclockdemoctl.xaml.cs
++CardDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\carddemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\carddemoctl.xaml.cs
++CarouselDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\carouseldemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\carouseldemoctl.xaml.cs
++ChatBubbleDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\chatbubbledemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\chatbubbledemoctl.xaml.cs
++CheckComboBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\checkcomboboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\checkcomboboxdemoctl.xaml.cs
++CirclePanelDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\circlepaneldemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\circlepaneldemoctl.xaml.cs
++ClockDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\clockdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\clockdemoctl.xaml.cs
++ColorPickerDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\colorpickerdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\colorpickerdemoctl.xaml.cs
++ComboBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\comboboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\comboboxdemoctl.xaml.cs
++CompareSliderDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\comparesliderdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\comparesliderdemoctl.xaml.cs
++CoverFlowDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\coverflowdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\coverflowdemoctl.xaml.cs
++CoverViewDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\coverviewdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\coverviewdemoctl.xaml.cs
++DatePickerDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\datepickerdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\datepickerdemoctl.xaml.cs
++DialogDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\dialogdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\dialogdemoctl.xaml.cs
++DividerDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\dividerdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\dividerdemoctl.xaml.cs
++DrawerDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\drawerdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\drawerdemoctl.xaml.cs
++ElementGroupDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\elementgroupdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\elementgroupdemoctl.xaml.cs
++FlexPanelDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\flexpaneldemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\flexpaneldemoctl.xaml.cs
++FlipClockDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\flipclockdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\flipclockdemoctl.xaml.cs
++FloatingBlockDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\floatingblockdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\floatingblockdemoctl.xaml.cs
++GifImageDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\gifimagedemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\gifimagedemoctl.xaml.cs
++GotoTopDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\gototopdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\gototopdemoctl.xaml.cs
++GravatarDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\gravatardemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\gravatardemoctl.xaml.cs
++GridDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\griddemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\griddemoctl.xaml.cs
++GrowlDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\growldemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\growldemoctl.xaml.cs
++HoneycombPanelDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\honeycombpaneldemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\honeycombpaneldemoctl.xaml.cs
++ImageBlockDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\imageblockdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\imageblockdemoctl.xaml.cs
++ImageBrowserDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\imagebrowserdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\imagebrowserdemoctl.xaml.cs
++ImageSelectorDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\imageselectordemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\imageselectordemoctl.xaml.cs
++LoadingDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\loadingdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\loadingdemoctl.xaml.cs
++MagnifierDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\magnifierdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\magnifierdemoctl.xaml.cs
++NotificationDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\notificationdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\notificationdemoctl.xaml.cs
++NotifyIconDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\notifyicondemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\notifyicondemoctl.xaml.cs
++OutlineTextDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\outlinetextdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\outlinetextdemoctl.xaml.cs
++PaginationDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\paginationdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\paginationdemoctl.xaml.cs
++PasswordBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\passwordboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\passwordboxdemoctl.xaml.cs
++PinBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\pinboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\pinboxdemoctl.xaml.cs
++PoptipDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\poptipdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\poptipdemoctl.xaml.cs
++PreviewSliderDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\previewsliderdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\previewsliderdemoctl.xaml.cs
++ProgressBarDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\progressbardemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\progressbardemoctl.xaml.cs
++ProgressButtonDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\progressbuttondemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\progressbuttondemoctl.xaml.cs
++PropertyGridDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\propertygriddemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\propertygriddemoctl.xaml.cs
++RangeSliderDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\rangesliderdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\rangesliderdemoctl.xaml.cs
++RateDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\ratedemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\ratedemoctl.xaml.cs
++RelativePanelDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\relativepaneldemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\relativepaneldemoctl.xaml.cs
++RunningBlockDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\runningblockdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\runningblockdemoctl.xaml.cs
++ScreenshotDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\screenshotdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\screenshotdemoctl.xaml.cs
++ScrollViewerDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\scrollviewerdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\scrollviewerdemoctl.xaml.cs
++SearchBarDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\searchbardemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\searchbardemoctl.xaml.cs
++ShieldDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\shielddemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\shielddemoctl.xaml.cs
++SideMenuDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\sidemenudemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\sidemenudemoctl.xaml.cs
++SplitButtonDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\splitbuttondemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\splitbuttondemoctl.xaml.cs
++SpriteDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\spritedemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\spritedemoctl.xaml.cs
++StepBarDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\stepbardemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\stepbardemoctl.xaml.cs
++TabControlDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\tabcontroldemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\tabcontroldemoctl.xaml.cs
++TagDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\tagdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\tagdemoctl.xaml.cs
++TextBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\textboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\textboxdemoctl.xaml.cs
++TimeBarDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\timebardemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\timebardemoctl.xaml.cs
++TransferDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\transferdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\transferdemoctl.xaml.cs
++TransitioningContentControlDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\transitioningcontentcontroldemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\transitioningcontentcontroldemoctl.xaml.cs
++UniformSpacingPanelDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\uniformspacingpaneldemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\uniformspacingpaneldemoctl.xaml.cs
++WaterfallPanelDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\waterfallpaneldemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\waterfallpaneldemoctl.xaml.cs
++WatermarkDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\watermarkdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\watermarkdemoctl.xaml.cs
++WindowDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\windowdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\controls\windowdemoctl.xaml.cs
++AppNotification.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\appnotification.xaml
++AppSprite.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\appsprite.xaml
++BlogsView.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\blogsview.xaml
++ContributorsView.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\contributorsview.xaml
++LeftMainContent.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\leftmaincontent.xaml
++MainContent.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\maincontent.xaml
++MainWindowContent.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\mainwindowcontent.xaml
++NonClientAreaContent.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\nonclientareacontent.xaml
++PracticalDemo.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\practicaldemo.xaml
++ProjectsView.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\projectsview.xaml
++UnderConstruction.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\underconstruction.xaml
++WebsitesView.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\websitesview.xaml
++Login.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\practical\login.xaml
++BorderDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\borderdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\borderdemoctl.xaml
++BrushDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\brushdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\brushdemoctl.xaml
++ButtonDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\buttondemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\buttondemoctl.xaml
++CalendarDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\calendardemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\calendardemoctl.xaml
++CheckBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\checkboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\checkboxdemoctl.xaml
++DataGridDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\datagriddemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\datagriddemoctl.xaml
++ExpanderDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\expanderdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\expanderdemoctl.xaml
++FlowDocumentDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\flowdocumentdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\flowdocumentdemoctl.xaml
++FrameDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\framedemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\framedemoctl.xaml
++GeometryDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\geometrydemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\geometrydemoctl.xaml
++GroupBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\groupboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\groupboxdemoctl.xaml
++LabelDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\labeldemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\labeldemoctl.xaml
++ListBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\listboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\listboxdemoctl.xaml
++ListViewDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\listviewdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\listviewdemoctl.xaml
++MenuDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\menudemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\menudemoctl.xaml
++NativeComboBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativecomboboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativecomboboxdemoctl.xaml
++NativeDatePickerDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativedatepickerdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativedatepickerdemoctl.xaml
++NativePasswordBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativepasswordboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativepasswordboxdemoctl.xaml
++NativeProgressBarDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativeprogressbardemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativeprogressbardemoctl.xaml
++NativeScrollViewerDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativescrollviewerdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativescrollviewerdemoctl.xaml
++NativeTabControlDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativetabcontroldemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativetabcontroldemoctl.xaml
++NativeTextBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativetextboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativetextboxdemoctl.xaml
++NativeWindowDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativewindowdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativewindowdemoctl.xaml
++RadioButtonDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\radiobuttondemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\radiobuttondemoctl.xaml
++RepeatButtonDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\repeatbuttondemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\repeatbuttondemoctl.xaml
++RichTextBoxDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\richtextboxdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\richtextboxdemoctl.xaml
++SliderDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\sliderdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\sliderdemoctl.xaml
++TextBlockDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\textblockdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\textblockdemoctl.xaml
++ToggleButtonDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\togglebuttondemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\togglebuttondemoctl.xaml
++ToolBarDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\toolbardemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\toolbardemoctl.xaml
++TreeViewDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\treeviewdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\treeviewdemoctl.xaml
++EffectsDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\effectsdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\effectsdemoctl.xaml
++GeometryAnimationDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\geometryanimationdemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\geometryanimationdemoctl.xaml
++HatchBrushGeneratorDemoCtl.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\hatchbrushgeneratordemoctl.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\hatchbrushgeneratordemoctl.xaml
++InputElementDemoViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\common\inputelementdemoviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\common\inputelementdemoviewmodel.cs
++ItemsDisplayViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\main\itemsdisplayviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\main\itemsdisplayviewmodel.cs
++MainViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\main\mainviewmodel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\main\mainviewmodel.cs
++NonClientAreaViewModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\main\nonclientareaviewmodel.cs
++AboutWindow.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\aboutwindow.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\aboutwindow.xaml
++BlurWindow.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\blurwindow.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\blurwindow.xaml
++CommonWindow.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\commonwindow.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\commonwindow.xaml
++CustomNonClientAreaWindow.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\customnonclientareawindow.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\customnonclientareawindow.xaml
++DialogDemoWindow.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\dialogdemowindow.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\dialogdemowindow.xaml
++GlowWindow.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\glowwindow.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\glowwindow.xaml
++GrowlDemoWindow.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\growldemowindow.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\growldemowindow.xaml
++NativeCommonWindow.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\nativecommonwindow.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\nativecommonwindow.xaml
++NavigationWindow.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\navigationwindow.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\navigationwindow.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\navigationwindow.xaml
++NoNonClientAreaDragableWindow.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\nononclientareadragablewindow.xaml
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\nononclientareadragablewindow.xaml
++TouchDragMoveWindow.xaml
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\touchdragmovewindow.xaml
++App.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\app.xaml.cs
++MainWindow.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\mainwindow.xaml.cs
++DemoType.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\enum\demotype.cs
++AvatarModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\model\avatarmodel.cs
++CardModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\model\cardmodel.cs
++ChatInfoModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\model\chatinfomodel.cs
++CoverViewDemoModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\model\coverviewdemomodel.cs
++DemoDataModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\model\demodatamodel.cs
++DemoInfoModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\model\demoinfomodel.cs
++DemoItemModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\model\demoitemmodel.cs
++GeometryItemModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\model\geometryitemmodel.cs
++PropertyGridDemoModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\model\propertygriddemomodel.cs
++StepBarDemoModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\model\stepbardemomodel.cs
++TabControlDemoModel.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\data\model\tabcontroldemomodel.cs
++DataService.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\service\data\dataservice.cs
++Avatar.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\avatar.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\avatar.xaml.cs
++ChatBox.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\chatbox.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\chatbox.xaml.cs
++GeometryItem.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\geometryitem.xaml.cs
++InteractiveDialog.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\interactivedialog.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\interactivedialog.xaml.cs
++TextDialog.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\textdialog.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\textdialog.xaml.cs
++TextDialogWithTimer.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\textdialogwithtimer.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\basic\textdialogwithtimer.xaml.cs
++AppNotification.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\appnotification.xaml.cs
++AppSprite.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\appsprite.xaml.cs
++BlogsView.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\blogsview.xaml.cs
++ContributorsView.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\contributorsview.xaml.cs
++LeftMainContent.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\leftmaincontent.xaml.cs
++MainContent.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\maincontent.xaml.cs
++MainWindowContent.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\mainwindowcontent.xaml.cs
++NonClientAreaContent.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\nonclientareacontent.cs
++PracticalDemo.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\practicaldemo.cs
++ProjectsView.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\projectsview.xaml.cs
++UnderConstruction.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\underconstruction.xaml.cs
++WebsitesView.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\main\websitesview.xaml.cs
++Login.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\practical\login.xaml.cs
++BorderDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\borderdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\borderdemoctl.xaml.cs
++BrushDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\brushdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\brushdemoctl.xaml.cs
++ButtonDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\buttondemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\buttondemoctl.xaml.cs
++CalendarDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\calendardemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\calendardemoctl.xaml.cs
++CheckBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\checkboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\checkboxdemoctl.xaml.cs
++DataGridDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\datagriddemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\datagriddemoctl.xaml.cs
++ExpanderDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\expanderdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\expanderdemoctl.xaml.cs
++FlowDocumentDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\flowdocumentdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\flowdocumentdemoctl.xaml.cs
++FrameDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\framedemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\framedemoctl.xaml.cs
++GeometryDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\geometrydemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\geometrydemoctl.xaml.cs
++GroupBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\groupboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\groupboxdemoctl.xaml.cs
++LabelDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\labeldemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\labeldemoctl.xaml.cs
++ListBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\listboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\listboxdemoctl.xaml.cs
++ListViewDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\listviewdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\listviewdemoctl.xaml.cs
++MenuDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\menudemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\menudemoctl.xaml.cs
++NativeComboBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativecomboboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativecomboboxdemoctl.xaml.cs
++NativeDatePickerDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativedatepickerdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativedatepickerdemoctl.xaml.cs
++NativePasswordBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativepasswordboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativepasswordboxdemoctl.xaml.cs
++NativeProgressBarDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativeprogressbardemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativeprogressbardemoctl.xaml.cs
++NativeScrollViewerDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativescrollviewerdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativescrollviewerdemoctl.xaml.cs
++NativeTabControlDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativetabcontroldemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativetabcontroldemoctl.xaml.cs
++NativeTextBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativetextboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativetextboxdemoctl.xaml.cs
++NativeWindowDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativewindowdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\nativewindowdemoctl.xaml.cs
++RadioButtonDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\radiobuttondemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\radiobuttondemoctl.xaml.cs
++RepeatButtonDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\repeatbuttondemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\repeatbuttondemoctl.xaml.cs
++RichTextBoxDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\richtextboxdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\richtextboxdemoctl.xaml.cs
++SliderDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\sliderdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\sliderdemoctl.xaml.cs
++TextBlockDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\textblockdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\textblockdemoctl.xaml.cs
++ToggleButtonDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\togglebuttondemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\togglebuttondemoctl.xaml.cs
++ToolBarDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\toolbardemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\toolbardemoctl.xaml.cs
++TreeViewDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\treeviewdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\styles\treeviewdemoctl.xaml.cs
++EffectsDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\effectsdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\effectsdemoctl.xaml.cs
++GeometryAnimationDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\geometryanimationdemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\geometryanimationdemoctl.xaml.cs
++HatchBrushGeneratorDemoCtl.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\hatchbrushgeneratordemoctl.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\usercontrol\tools\hatchbrushgeneratordemoctl.xaml.cs
++AboutWindow.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\aboutwindow.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\aboutwindow.xaml.cs
++BlurWindow.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\blurwindow.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\blurwindow.xaml.cs
++CommonWindow.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\commonwindow.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\commonwindow.xaml.cs
++CustomNonClientAreaWindow.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\customnonclientareawindow.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\customnonclientareawindow.xaml.cs
++DialogDemoWindow.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\dialogdemowindow.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\dialogdemowindow.xaml.cs
++GlowWindow.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\glowwindow.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\glowwindow.xaml.cs
++GrowlDemoWindow.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\growldemowindow.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\growldemowindow.xaml.cs
++NativeCommonWindow.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\nativecommonwindow.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\nativecommonwindow.xaml.cs
++NavigationWindow.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\navigationwindow.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\navigationwindow.xaml.cs
++NoNonClientAreaDragableWindow.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\nononclientareadragablewindow.xaml.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\nononclientareadragablewindow.xaml.cs
++TouchDragMoveWindow.xaml.cs
i:{c4694269-c9b8-45d5-87f8-d0088c532510}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\window\touchdragmovewindow.xaml.cs
++Collections
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\collections\
++Pool
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\collections\pool\
++Queue
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\collections\queue\
++ManualObservableCollection`1.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\collections\manualobservablecollection`1.cs
++Attach
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\
++BackgroundSwitchElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\backgroundswitchelement.cs
++BorderElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\borderelement.cs
++ComboBoxAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\comboboxattach.cs
++DataGridAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\datagridattach.cs
++DropDownElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\dropdownelement.cs
++EdgeElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\edgeelement.cs
++GridAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\gridattach.cs
++GridViewAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\gridviewattach.cs
++IconElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\iconelement.cs
++IconSwitchElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\iconswitchelement.cs
++ImageAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\imageattach.cs
++InfoElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\infoelement.cs
++ListBoxAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\listboxattach.cs
++MenuAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\menuattach.cs
++MenuItemAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\menuitemattach.cs
++MenuTopLineAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\menutoplineattach.cs
++PanelElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\panelelement.cs
++PasswordBoxAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\passwordboxattach.cs
++RectangleAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\rectangleattach.cs
++ScrollViewerAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\scrollviewerattach.cs
++StatusSwitchElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\statusswitchelement.cs
++TextBlockAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\textblockattach.cs
++TipElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\tipelement.cs
++TitleElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\titleelement.cs
++ToggleButtonAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\togglebuttonattach.cs
++VisualElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\visualelement.cs
++WindowAttach.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\attach\windowattach.cs
++Base
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\base\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\themes\styles\base\
++Block
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\block\
++Button
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\button\
++Carousel
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\carousel\
++ColorPicker
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\colorpicker\
++Cover
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\cover\
++Dialog
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\dialog\
++Drawer
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\drawer\
++Growl
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\growl\
++Image
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\image\
++Input
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\input\
++AutoCompleteTextBox
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\autocompletetextbox\
++CheckComboBox
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\checkcombobox\
++ComboBox.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\combobox.cs
++DatePicker.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\datepicker.cs
++DateTimePicker.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\datetimepicker.cs
++ImageSelector.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\imageselector.cs
++NumericUpDown.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\numericupdown.cs
++PasswordBox.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\passwordbox.cs
++PinBox.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\pinbox.cs
++SearchBar.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\searchbar.cs
++TextBox.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\textbox.cs
++TimePicker.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\timepicker.cs
++WatermarkTextBox.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\watermarktextbox.cs
++Loading
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\loading\
++Other
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\
++Panel
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\
++ProgressBar
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\progressbar\
++PropertyGrid
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\
++Rate
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\rate\
++Ribbon
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\ribbon\
++Ribbon.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\ribbon\ribbon.cs
++RibbonGroup.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\ribbon\ribbongroup.cs
++RibbonTab.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\ribbon\ribbontab.cs
++RibbonTabHeader.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\ribbon\ribbontabheader.cs
++RibbonTabHeaderItemsControl.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\ribbon\ribbontabheaderitemscontrol.cs
++Screenshot
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\screenshot\
++SideMenu
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\sidemenu\
++Slider
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\slider\
++StepBar
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\stepbar\
++TabControl
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\tabcontrol\
++Tag
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\tag\
++Text
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\text\
++Time
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\
++Transfer
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\transfer\
++Args
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\args\
++Flex
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\flex\
++Gif
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\gif\
++Info
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\info\
++Operation
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\operation\
++Range
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\range\
++DisposableObject.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\disposableobject.cs
++EnumItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enumitem.cs
++HandyControlConfig.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\handycontrolconfig.cs
++HwndWrapper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\hwndwrapper.cs
++PanelUvSize.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\paneluvsize.cs
++ResourceToken.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\resourcetoken.cs
++ValueBoxes.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\valueboxes.cs
++Interactivity
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\interactivity\
++Icons
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\
++HandyControl.Controls.AnimationPath.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.animationpath.icon.bmp
++HandyControl.Controls.AxleCanvas.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.axlecanvas.icon.bmp
++HandyControl.Controls.Badge.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.badge.icon.bmp
++HandyControl.Controls.BlendEffectBox.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.blendeffectbox.icon.bmp
++HandyControl.Controls.ButtonGroup.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.buttongroup.icon.bmp
++HandyControl.Controls.CalendarWithClock.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.calendarwithclock.icon.bmp
++HandyControl.Controls.Card.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.card.icon.bmp
++HandyControl.Controls.Carousel.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.carousel.icon.bmp
++HandyControl.Controls.CheckComboBox.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.checkcombobox.icon.bmp
++HandyControl.Controls.CirclePanel.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.circlepanel.icon.bmp
++HandyControl.Controls.CircleProgressBar.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.circleprogressbar.icon.bmp
++HandyControl.Controls.Clock.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.clock.icon.bmp
++HandyControl.Controls.Col.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.col.icon.bmp
++HandyControl.Controls.ColorPicker.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.colorpicker.icon.bmp
++HandyControl.Controls.CompareSlider.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.compareslider.icon.bmp
++HandyControl.Controls.ContextMenuButton.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.contextmenubutton.icon.bmp
++HandyControl.Controls.ContextMenuToggleButton.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.contextmenutogglebutton.icon.bmp
++HandyControl.Controls.CoverFlow.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.coverflow.icon.bmp
++HandyControl.Controls.CoverView.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.coverview.icon.bmp
++HandyControl.Controls.DashedBorder.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.dashedborder.icon.bmp
++HandyControl.Controls.DateTimePicker.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.datetimepicker.icon.bmp
++HandyControl.Controls.Dialog.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.dialog.icon.bmp
++HandyControl.Controls.Divider.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.divider.icon.bmp
++HandyControl.Controls.Drawer.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.drawer.icon.bmp
++HandyControl.Controls.Empty.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.empty.icon.bmp
++HandyControl.Controls.FlexPanel.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.flexpanel.icon.bmp
++HandyControl.Controls.FlipClock.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.flipclock.icon.bmp
++HandyControl.Controls.GifImage.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.gifimage.icon.bmp
++HandyControl.Controls.GotoTop.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.gototop.icon.bmp
++HandyControl.Controls.Gravatar.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.gravatar.icon.bmp
++HandyControl.Controls.Growl.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.growl.icon.bmp
++HandyControl.Controls.HoneycombPanel.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.honeycombpanel.icon.bmp
++HandyControl.Controls.ImageBlock.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.imageblock.icon.bmp
++HandyControl.Controls.ImageSelector.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.imageselector.icon.bmp
++HandyControl.Controls.ImageViewer.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.imageviewer.icon.bmp
++HandyControl.Controls.ListClock.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.listclock.icon.bmp
++HandyControl.Controls.LoadingCircle.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.loadingcircle.icon.bmp
++HandyControl.Controls.LoadingLine.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.loadingline.icon.bmp
++HandyControl.Controls.Magnifier.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.magnifier.icon.bmp
++HandyControl.Controls.NotifyIcon.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.notifyicon.icon.bmp
++HandyControl.Controls.NumericUpDown.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.numericupdown.icon.bmp
++HandyControl.Controls.OutlineText.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.outlinetext.icon.bmp
++HandyControl.Controls.Pagination.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.pagination.icon.bmp
++HandyControl.Controls.PinBox.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.pinbox.icon.bmp
++HandyControl.Controls.Poptip.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.poptip.icon.bmp
++HandyControl.Controls.PreviewSlider.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.previewslider.icon.bmp
++HandyControl.Controls.ProgressButton.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.progressbutton.icon.bmp
++HandyControl.Controls.PropertyGrid.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.propertygrid.icon.bmp
++HandyControl.Controls.RangeSlider.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.rangeslider.icon.bmp
++HandyControl.Controls.RelativePanel.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.relativepanel.icon.bmp
++HandyControl.Controls.Row.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.row.icon.bmp
++HandyControl.Controls.RunningBlock.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.runningblock.icon.bmp
++HandyControl.Controls.SearchBar.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.searchbar.icon.bmp
++HandyControl.Controls.Shield.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.shield.icon.bmp
++HandyControl.Controls.SideMenu.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.sidemenu.icon.bmp
++HandyControl.Controls.SimplePanel.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.simplepanel.icon.bmp
++HandyControl.Controls.SimpleStackPanel.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.simplestackpanel.icon.bmp
++HandyControl.Controls.SimpleText.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.simpletext.icon.bmp
++HandyControl.Controls.SplitButton.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.splitbutton.icon.bmp
++HandyControl.Controls.StepBar.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.stepbar.icon.bmp
++HandyControl.Controls.Tag.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.tag.icon.bmp
++HandyControl.Controls.TagContainer.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.tagcontainer.icon.bmp
++HandyControl.Controls.TimeBar.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.timebar.icon.bmp
++HandyControl.Controls.TimePicker.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.timepicker.icon.bmp
++HandyControl.Controls.ToggleBlock.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.toggleblock.icon.bmp
++HandyControl.Controls.Transfer.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.transfer.icon.bmp
++HandyControl.Controls.TransitioningContentControl.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.transitioningcontentcontrol.icon.bmp
++HandyControl.Controls.UniformSpacingPanel.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.uniformspacingpanel.icon.bmp
++HandyControl.Controls.WaterfallPanel.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.waterfallpanel.icon.bmp
++HandyControl.Controls.Watermark.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.watermark.icon.bmp
++HandyControl.Controls.WatermarkTextBox.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.watermarktextbox.icon.bmp
++HandyControl.Controls.WaveProgressBar.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.controls.waveprogressbar.icon.bmp
++HandyControl.Expression.Shapes.Arc.icon.bmp
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\icons\handycontrol.expression.shapes.arc.icon.bmp
++AutoCompleteTextBoxBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\autocompletetextboxbasestyle.xaml
++BadgeBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\badgebasestyle.xaml
++BaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\basestyle.xaml
++ButtonBaseBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\buttonbasebasestyle.xaml
++ButtonBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\buttonbasestyle.xaml
++ButtonGroupBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\buttongroupbasestyle.xaml
++CardBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\cardbasestyle.xaml
++ChatBubbleBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\chatbubblebasestyle.xaml
++CheckBoxBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\checkboxbasestyle.xaml
++CheckComboBoxBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\checkcomboboxbasestyle.xaml
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\themes\styles\base\checkcomboboxbasestyle.xaml
++ColorPickerBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\colorpickerbasestyle.xaml
++ComboBoxBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\comboboxbasestyle.xaml
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\themes\styles\base\comboboxbasestyle.xaml
++ContextMenuBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\contextmenubasestyle.xaml
++CoverFlowBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\coverflowbasestyle.xaml
++CoverViewBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\coverviewbasestyle.xaml
++DataGridBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\datagridbasestyle.xaml
++DatePickerBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\datepickerbasestyle.xaml
++DateTimePickerBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\datetimepickerbasestyle.xaml
++DialogBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\dialogbasestyle.xaml
++DividerBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\dividerbasestyle.xaml
++DrawerBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\drawerbasestyle.xaml
++ElementGroupBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\elementgroupbasestyle.xaml
++EmptyBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\emptybasestyle.xaml
++ExpanderBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\expanderbasestyle.xaml
++FlowDocumentBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\flowdocumentbasestyle.xaml
++FrameBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\framebasestyle.xaml
++GotoTopBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\gototopbasestyle.xaml
++GravatarBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\gravatarbasestyle.xaml
++GroupBoxBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\groupboxbasestyle.xaml
++ImageSelectorBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\imageselectorbasestyle.xaml
++ItemsPanelTemplate.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\itemspaneltemplate.xaml
++LabelBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\labelbasestyle.xaml
++ListBoxBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\listboxbasestyle.xaml
++ListViewBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\listviewbasestyle.xaml
++LoadingBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\loadingbasestyle.xaml
++LoadingCircleBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\loadingcirclebasestyle.xaml
++LoadingLineBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\loadinglinebasestyle.xaml
++MagnifierBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\magnifierbasestyle.xaml
++MenuBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\menubasestyle.xaml
++NavigationWindowBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\navigationwindowbasestyle.xaml
++NumericUpDownBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\numericupdownbasestyle.xaml
++PasswordBoxBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\passwordboxbasestyle.xaml
++PinBoxBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\pinboxbasestyle.xaml
++PoptipBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\poptipbasestyle.xaml
++ProgressBarBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\progressbarbasestyle.xaml
++ProgressButtonBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\progressbuttonbasestyle.xaml
++PropertyGridBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\propertygridbasestyle.xaml
++RadioButtonBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\radiobuttonbasestyle.xaml
++RateBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\ratebasestyle.xaml
++RepeatButtonBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\repeatbuttonbasestyle.xaml
++RunningBlockBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\runningblockbasestyle.xaml
++ScrollViewerBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\scrollviewerbasestyle.xaml
++SearchBarBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\searchbarbasestyle.xaml
++SeparatorBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\separatorbasestyle.xaml
++ShieldBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\shieldbasestyle.xaml
++SideMenuBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\sidemenubasestyle.xaml
++SliderBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\sliderbasestyle.xaml
++SplitButtonBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\splitbuttonbasestyle.xaml
++StatusBarBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\statusbarbasestyle.xaml
++StepBarBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\stepbarbasestyle.xaml
++TabControlBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\tabcontrolbasestyle.xaml
++TagBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\tagbasestyle.xaml
++TextBlockBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\textblockbasestyle.xaml
++TextBoxBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\textboxbasestyle.xaml
++TimePickerBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\timepickerbasestyle.xaml
++ToggleBlockBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\toggleblockbasestyle.xaml
++ToggleButtonBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\togglebuttonbasestyle.xaml
++ToolBarBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\toolbarbasestyle.xaml
++ToolTipBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\tooltipbasestyle.xaml
++TransferBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\transferbasestyle.xaml
++TreeViewBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\treeviewbasestyle.xaml
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\themes\styles\base\treeviewbasestyle.xaml
++WatermarkBaseStyle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\base\watermarkbasestyle.xaml
++AutoCompleteTextBox.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\autocompletetextbox.xaml
++Badge.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\badge.xaml
++BlendEffectBox.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\blendeffectbox.xaml
++Border.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\border.xaml
++Button.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\button.xaml
++ButtonGroup.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\buttongroup.xaml
++Calendar.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\calendar.xaml
++CalendarWithClock.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\calendarwithclock.xaml
++Card.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\card.xaml
++Carousel.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\carousel.xaml
++ChatBubble.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\chatbubble.xaml
++CheckBox.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\checkbox.xaml
++CheckComboBox.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\checkcombobox.xaml
++Clock.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\clock.xaml
++Col.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\col.xaml
++ColorPicker.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\colorpicker.xaml
++ComboBox.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\combobox.xaml
++ContentControl.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\contentcontrol.xaml
++ContextMenu.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\contextmenu.xaml
++CoverFlow.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\coverflow.xaml
++CoverView.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\coverview.xaml
++DataGrid.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\datagrid.xaml
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\themes\styles\datagrid.xaml
++DatePicker.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\datepicker.xaml
++DateTimePicker.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\datetimepicker.xaml
++Dialog.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\dialog.xaml
++Divider.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\divider.xaml
++Drawer.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\drawer.xaml
++ElementGroup.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\elementgroup.xaml
++Empty.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\empty.xaml
++Expander.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\expander.xaml
++FlipClock.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\flipclock.xaml
++FloatingBlock.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\floatingblock.xaml
++FlowDocument.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\flowdocument.xaml
++Frame.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\frame.xaml
++GotoTop.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\gototop.xaml
++Gravatar.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\gravatar.xaml
++GridSplitter.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\gridsplitter.xaml
++GroupBox.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\groupbox.xaml
++Growl.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\growl.xaml
++ImageSelector.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\imageselector.xaml
++ImageViewer.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\imageviewer.xaml
++Label.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\label.xaml
++ListBox.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\listbox.xaml
++ListView.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\listview.xaml
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\themes\styles\listview.xaml
++Loading.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\loading.xaml
++Magnifier.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\magnifier.xaml
++Menu.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\menu.xaml
++MessageBox.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\messagebox.xaml
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\themes\styles\messagebox.xaml
++Notification.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\notification.xaml
++NumericUpDown.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\numericupdown.xaml
++Pagination.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\pagination.xaml
++PasswordBox.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\passwordbox.xaml
++PinBox.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\pinbox.xaml
++Poptip.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\poptip.xaml
++PopupWindow.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\popupwindow.xaml
++ProgressBar.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\progressbar.xaml
++ProgressButton.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\progressbutton.xaml
++PropertyGrid.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\propertygrid.xaml
++RadioButton.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\radiobutton.xaml
++Rate.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\rate.xaml
++Rectangle.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\rectangle.xaml
++RepeatButton.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\repeatbutton.xaml
++Ribbon.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\ribbon.xaml
++RichTextBox.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\richtextbox.xaml
++RunningBlock.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\runningblock.xaml
++Screenshot.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\screenshot.xaml
++ScrollViewer.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\scrollviewer.xaml
++SearchBar.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\searchbar.xaml
++Separator.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\separator.xaml
++Shield.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\shield.xaml
++SideMenu.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\sidemenu.xaml
++SimpleItemsControl.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\simpleitemscontrol.xaml
++Slider.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\slider.xaml
++SplitButton.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\splitbutton.xaml
++Sprite.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\sprite.xaml
++StatusBar.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\statusbar.xaml
++StepBar.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\stepbar.xaml
++TabControl.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\tabcontrol.xaml
++Tag.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\tag.xaml
++TextBlock.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\textblock.xaml
++TextBox.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\textbox.xaml
++TimeBar.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\timebar.xaml
++TimePicker.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\timepicker.xaml
++ToggleBlock.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\toggleblock.xaml
++ToggleButton.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\togglebutton.xaml
++ToolBar.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\toolbar.xaml
++ToolTip.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\tooltip.xaml
++Transfer.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\transfer.xaml
++TransitioningContentControl.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\transitioningcontentcontrol.xaml
++TreeView.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\treeview.xaml
++Watermark.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\watermark.xaml
++Window.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\styles\window.xaml
i:{32204503-2ef0-4681-ae13-aa1feb6c658a}:d:\projects\handycontrol-master\src\net_40\handycontrol_net_40\themes\styles\window.xaml
++Lang.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\lang.xaml
++SharedResourceDictionary.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\sharedresourcedictionary.cs
++SkinViolet.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\skinviolet.xaml
++Theme.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\theme.cs
++Theme_40.txt
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\theme_40.txt
++Theme_GE45.txt
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\theme_ge45.txt
++XamlCombine.exe
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\xamlcombine.exe
++IPool`1.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\collections\pool\ipool`1.cs
++SimplePool`1.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\collections\pool\simplepool`1.cs
++SynchronizedPool`1.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\collections\pool\synchronizedpool`1.cs
++Deque`1.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\collections\queue\deque`1.cs
++AdornerElement.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\base\adornerelement.cs
++HeaderedSelectableItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\base\headeredselectableitem.cs
++HeaderedSimpleItemsControl.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\base\headeredsimpleitemscontrol.cs
++IGravatarGenerator.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\base\igravatargenerator.cs
++ISelectable.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\base\iselectable.cs
++ISingleOpen.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\base\isingleopen.cs
++RegularItemsControl.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\base\regularitemscontrol.cs
++SelectableItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\base\selectableitem.cs
++SimpleItemsControl.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\base\simpleitemscontrol.cs
++RunningBlock
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\block\runningblock\
++FloatingBlock.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\block\floatingblock.cs
++ToggleBlock.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\block\toggleblock.cs
++ButtonGroup.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\button\buttongroup.cs
++ContextMenuButton.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\button\contextmenubutton.cs
++ContextMenuToggleButton.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\button\contextmenutogglebutton.cs
++ProgressButton.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\button\progressbutton.cs
++SplitButton.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\button\splitbutton.cs
++Carousel.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\carousel\carousel.cs
++CarouselItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\carousel\carouselitem.cs
++ColorDropper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\colorpicker\colordropper.cs
++ColorPicker.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\colorpicker\colorpicker.cs
++CoverFlow
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\cover\coverflow\
++CoverView
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\cover\coverview\
++Dialog.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\dialog\dialog.cs
++DialogContainer.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\dialog\dialogcontainer.cs
++Drawer.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\drawer\drawer.cs
++DrawerContainer.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\drawer\drawercontainer.cs
++Growl.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\growl\growl.cs
++GrowlWindow.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\growl\growlwindow.cs
++GifImage.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\image\gifimage.cs
++ImageBlock.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\image\imageblock.cs
++ImageViewer.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\image\imageviewer.cs
++AutoCompleteTextBox.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\autocompletetextbox\autocompletetextbox.cs
++AutoCompleteTextBoxItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\autocompletetextbox\autocompletetextboxitem.cs
++CheckComboBox.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\checkcombobox\checkcombobox.cs
++CheckComboBoxItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\input\checkcombobox\checkcomboboxitem.cs
++LoadingBase.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\loading\loadingbase.cs
++LoadingCircle.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\loading\loadingcircle.cs
++LoadingLine.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\loading\loadingline.cs
++AnimationPath.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\animationpath.cs
++Badge.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\badge.cs
++BlendEffectBox.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\blendeffectbox.cs
++Card.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\card.cs
++ChatBubble.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\chatbubble.cs
++DashedBorder.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\dashedborder.cs
++Divider.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\divider.cs
++Empty.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\empty.cs
++GotoTop.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\gototop.cs
++Gravatar.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\gravatar.cs
++Magnifier.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\magnifier.cs
++Notification.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\notification.cs
++NotifyIcon.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\notifyicon.cs
++Pagination.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\pagination.cs
++Poptip.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\poptip.cs
++ScrollViewer.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\scrollviewer.cs
++Shield.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\shield.cs
++Sprite.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\sprite.cs
++TransitioningContentControl.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\transitioningcontentcontrol.cs
++Watermark.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\other\watermark.cs
++Grid
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\grid\
++AxleCanvas.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\axlecanvas.cs
++CirclePanel.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\circlepanel.cs
++ClipGrid.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\clipgrid.cs
++ElementGroup.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\elementgroup.cs
++FlexPanel.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\flexpanel.cs
++HoneycombPanel.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\honeycombpanel.cs
++RelativePanel.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\relativepanel.cs
++SimplePanel.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\simplepanel.cs
++SimpleStackPanel.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\simplestackpanel.cs
++UniformSpacingPanel.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\uniformspacingpanel.cs
++WaterfallPanel.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\waterfallpanel.cs
++CircleProgressBar.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\progressbar\circleprogressbar.cs
++WaveProgressBar.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\progressbar\waveprogressbar.cs
++Editors
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\
++PropertyGrid.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\propertygrid.cs
++PropertyItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\propertyitem.cs
++PropertyItemsControl.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\propertyitemscontrol.cs
++PropertyResolver.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\propertyresolver.cs
++Rate.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\rate\rate.cs
++RateItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\rate\rateitem.cs
++Screenshot.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\screenshot\screenshot.cs
++ScreenshotWindow.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\screenshot\screenshotwindow.cs
++SideMenu.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\sidemenu\sidemenu.cs
++SideMenuItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\sidemenu\sidemenuitem.cs
++CompareSlider
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\slider\compareslider\
++RangeSlider
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\slider\rangeslider\
++PreviewSlider.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\slider\previewslider.cs
++StepBar.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\stepbar\stepbar.cs
++StepBarItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\stepbar\stepbaritem.cs
++SlidingTabContainer.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\tabcontrol\slidingtabcontainer.cs
++TabControl.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\tabcontrol\tabcontrol.cs
++TabItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\tabcontrol\tabitem.cs
++TabPanel.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\tabcontrol\tabpanel.cs
++Tag.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\tag\tag.cs
++TagContainer.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\tag\tagcontainer.cs
++HighlightTextBlock.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\text\highlighttextblock.cs
++OutlineText.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\text\outlinetext.cs
++SimpleText.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\text\simpletext.cs
++Clock
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\clock\
++FlipClock
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\flipclock\
++TimeBar
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\timebar\
++CalendarWithClock.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\calendarwithclock.cs
++Transfer.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\transfer\transfer.cs
++TransferItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\transfer\transferitem.cs
++BlurWindow.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\window\blurwindow.cs
++GlowWindow.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\window\glowwindow.cs
++ImageBrowser.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\window\imagebrowser.cs
++MessageBox.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\window\messagebox.cs
++PopupWindow.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\window\popupwindow.cs
++Window.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\window\window.cs
++CancelRoutedEventArgs.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\args\cancelroutedeventargs.cs
++FunctionEventArgs`1.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\args\functioneventargs`1.cs
++KeyboardHookEventArgs.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\args\keyboardhookeventargs.cs
++MouseHookEventArgs.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\args\mousehookeventargs.cs
++BadgeStatus.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\badgestatus.cs
++ChatMessageType.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\chatmessagetype.cs
++ChatRoleType.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\chatroletype.cs
++ColLayoutStatus.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\collayoutstatus.cs
++DrawerShowMode.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\drawershowmode.cs
++EnumDataProvider.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\enumdataprovider.cs
++ExpandMode.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\expandmode.cs
++GrowlShowMode.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\growlshowmode.cs
++HatchStyle.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\hatchstyle.cs
++HitMode.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\hitmode.cs
++ImageCodecFlags.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\imagecodecflags.cs
++InfoType.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\infotype.cs
++IpType.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\iptype.cs
++LinearLayout.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\linearlayout.cs
++MouseHookMessageType.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\mousehookmessagetype.cs
++NotifyIconInfoType.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\notifyiconinfotype.cs
++PlacementType.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\placementtype.cs
++ResultType.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\resulttype.cs
++RunningDirection.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\runningdirection.cs
++ShowAnimation.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\showanimation.cs
++SideMenuItemRole.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\sidemenuitemrole.cs
++SkinType.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\skintype.cs
++StepStatus.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\stepstatus.cs
++StrokePosition.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\strokeposition.cs
++TextType.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\texttype.cs
++TitlePlacementType.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\titleplacementtype.cs
++TransitionMode.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\transitionmode.cs
++VisualWrapping.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\enum\visualwrapping.cs
++FlexContentAlignment.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\flex\flexcontentalignment.cs
++FlexContentJustify.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\flex\flexcontentjustify.cs
++FlexDirection.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\flex\flexdirection.cs
++FlexItemAlignment.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\flex\flexitemalignment.cs
++FlexItemsAlignment.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\flex\flexitemsalignment.cs
++FlexWrap.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\flex\flexwrap.cs
++GifFrameDimension.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\gif\gifframedimension.cs
++GifImageInfo.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\gif\gifimageinfo.cs
++GifPropertyItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\gif\gifpropertyitem.cs
++GifPropertyItemInternal.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\gif\gifpropertyiteminternal.cs
++GPStream.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\gif\gpstream.cs
++ChangeScope.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\glowwindow\changescope.cs
++GlowBitmap.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\glowwindow\glowbitmap.cs
++GlowBitmapPart.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\glowwindow\glowbitmappart.cs
++GlowDrawingContext.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\glowwindow\glowdrawingcontext.cs
++GlowEdge.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\glowwindow\glowedge.cs
++GrowlInfo.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\info\growlinfo.cs
++ImageCodecInfo.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\info\imagecodecinfo.cs
++MessageBoxInfo.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\info\messageboxinfo.cs
++SystemVersionInfo.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\info\systemversioninfo.cs
++OperationResult.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\operation\operationresult.cs
++OperationResult`1.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\operation\operationresult`1.cs
++ColorRange.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\range\colorrange.cs
++DateTimeRange.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\range\datetimerange.cs
++DoubleRange.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\range\doublerange.cs
++IValueRange.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\data\range\ivaluerange.cs
++SimpleMouseBinding.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\input\simplemousebinding.cs
++Commands
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\interactivity\commands\
++EventToCommand.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\interactivity\eventtocommand.cs
++IEventArgsConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\interactivity\ieventargsconverter.cs
++MouseDragElementBehaviorEx.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\interactivity\mousedragelementbehaviorex.cs
++RoutedEventTrigger.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\interactivity\routedeventtrigger.cs
++Animation
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\animation\
++BrightnessEffect.fx
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\brightnesseffect.fx
++ColorComplementEffect.fx
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\colorcomplementeffect.fx
++ColorMatrixEffect.fx
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\colormatrixeffect.fx
++ContrastEffect.fx
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\contrasteffect.fx
++GrayScaleEffect.fx
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\resources\effects\grayscaleeffect.fx
++Behaviors.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\behaviors.xaml
++Effects.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\effects.xaml
++Paths.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\paths.xaml
++Sizes.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\sizes.xaml
++Generator
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\generator\
++Hook
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\hook\
++Interop
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\interop\
++StyleSelector
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\styleselector\
++GifImageAnimator.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\gifimageanimator.cs
++GlobalShortcut.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\globalshortcut.cs
++RegexJudgment.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\regexjudgment.cs
++RegexPatterns.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\regexpatterns.cs
++RunningBlock.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\block\runningblock\runningblock.cs
++RunningBorder.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\block\runningblock\runningborder.cs
++CoverFlow.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\cover\coverflow\coverflow.cs
++CoverFlowItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\cover\coverflow\coverflowitem.cs
++CoverView.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\cover\coverview\coverview.cs
++CoverViewContent.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\cover\coverview\coverviewcontent.cs
++CoverViewItem.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\cover\coverview\coverviewitem.cs
++Col.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\grid\col.cs
++Row.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\panel\grid\row.cs
++DatePropertyEditor.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\datepropertyeditor.cs
++DateTimePropertyEditor.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\datetimepropertyeditor.cs
++EnumPropertyEditor.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\enumpropertyeditor.cs
++HorizontalAlignmentPropertyEditor.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\horizontalalignmentpropertyeditor.cs
++ImagePropertyEditor.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\imagepropertyeditor.cs
++NumberPropertyEditor.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\numberpropertyeditor.cs
++PlainTextPropertyEditor.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\plaintextpropertyeditor.cs
++PropertyEditorBase.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\propertyeditorbase.cs
++ReadOnlyTextPropertyEditor.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\readonlytextpropertyeditor.cs
++SwitchPropertyEditor.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\switchpropertyeditor.cs
++TimePropertyEditor.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\timepropertyeditor.cs
++VerticalAlignmentPropertyEditor.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\propertygrid\editors\verticalalignmentpropertyeditor.cs
++CompareSlider.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\slider\compareslider\compareslider.cs
++CompareTrack.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\slider\compareslider\comparetrack.cs
++RangeSlider.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\slider\rangeslider\rangeslider.cs
++RangeThumb.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\slider\rangeslider\rangethumb.cs
++RangeTrack.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\slider\rangeslider\rangetrack.cs
++TwoWayRangeBase.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\slider\rangeslider\twowayrangebase.cs
++Clock.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\clock\clock.cs
++ClockBase.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\clock\clockbase.cs
++ClockRadioButton.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\clock\clockradiobutton.cs
++ListClock.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\clock\listclock.cs
++FlipClock.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\flipclock\flipclock.cs
++FlipNumber.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\flipclock\flipnumber.cs
++SpeTextBlock.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\timebar\spetextblock.cs
++TimeBar.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\controls\time\timebar\timebar.cs
++CloseWindowCommand.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\interactivity\commands\closewindowcommand.cs
++ControlCommands.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\interactivity\commands\controlcommands.cs
++OpenLinkCommand.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\interactivity\commands\openlinkcommand.cs
++PushMainWindow2TopCommand.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\interactivity\commands\pushmainwindow2topcommand.cs
++ShutdownAppCommand.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\interactivity\commands\shutdownappcommand.cs
++StartScreenshotCommand.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\interactivity\commands\startscreenshotcommand.cs
++DiscreteGeometryKeyFrame.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\animation\discretegeometrykeyframe.cs
++EasingGeometryKeyFrame.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\animation\easinggeometrykeyframe.cs
++GeometryAnimation.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\animation\geometryanimation.cs
++GeometryAnimationBase.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\animation\geometryanimationbase.cs
++GeometryAnimationUsingKeyFrames.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\animation\geometryanimationusingkeyframes.cs
++GeometryKeyFrame.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\animation\geometrykeyframe.cs
++GeometryKeyFrameCollection.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\animation\geometrykeyframecollection.cs
++LinearGeometryKeyFrame.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\animation\lineargeometrykeyframe.cs
++ResolvedKeyFrameEntry.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\animation\resolvedkeyframeentry.cs
++SplineGeometryKeyFrame.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\animation\splinegeometrykeyframe.cs
++BrightnessEffect.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\effects\brightnesseffect.cs
++ColorComplementEffect.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\effects\colorcomplementeffect.cs
++ColorMatrixEffect.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\effects\colormatrixeffect.cs
++ContrastEffect.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\effects\contrasteffect.cs
++EffectBase.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\effects\effectbase.cs
++GrayScaleEffect.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\media\effects\grayscaleeffect.cs
++ColorsViolet.xaml
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\themes\basic\colors\colorsviolet.xaml
++Boolean2BooleanReConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\boolean2booleanreconverter.cs
++Boolean2StrConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\boolean2strconverter.cs
++Boolean2VisibilityReConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\boolean2visibilityreconverter.cs
++BooleanArr2BooleanConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\booleanarr2booleanconverter.cs
++BooleanArr2VisibilityConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\booleanarr2visibilityconverter.cs
++BorderCircularClipConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\bordercircularclipconverter.cs
++BorderCircularConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\bordercircularconverter.cs
++BorderClipConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\borderclipconverter.cs
++ColLayoutConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\collayoutconverter.cs
++Color2ChannelAConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\color2channelaconverter.cs
++Color2HexStrConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\color2hexstrconverter.cs
++CornerRadiusSplitConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\cornerradiussplitconverter.cs
++DataGridSelectAllButtonVisibilityConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\datagridselectallbuttonvisibilityconverter.cs
++Double2GridLengthConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\double2gridlengthconverter.cs
++DoubleMinConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\doubleminconverter.cs
++Int2StrConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\int2strconverter.cs
++Long2FileSizeConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\long2filesizeconverter.cs
++Number2PercentageConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\number2percentageconverter.cs
++Object2BooleanConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\object2booleanconverter.cs
++Object2BooleanReConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\object2booleanreconverter.cs
++Object2StringConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\object2stringconverter.cs
++Object2VisibilityConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\object2visibilityconverter.cs
++Object2VisibilityReConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\object2visibilityreconverter.cs
++RectangleCircularConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\rectanglecircularconverter.cs
++String2VisibilityConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\string2visibilityconverter.cs
++String2VisibilityReConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\string2visibilityreconverter.cs
++ThicknessSplitConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\thicknesssplitconverter.cs
++TreeViewItemMarginConverter.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\converter\treeviewitemmarginconverter.cs
++ColLayout.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\extension\collayout.cs
++ColorExtension.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\extension\colorextension.cs
++DependencyObjectExtension.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\extension\dependencyobjectextension.cs
++DialogExtension.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\extension\dialogextension.cs
++EnumerableExtension.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\extension\enumerableextension.cs
++FrameworkElementExtension.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\extension\frameworkelementextension.cs
++GeometryExtension.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\extension\geometryextension.cs
++StringExtension.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\extension\stringextension.cs
++UIElementExtension.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\extension\uielementextension.cs
++ValueExtension.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\extension\valueextension.cs
++ComparerGenerator.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\generator\comparergenerator.cs
++DateTimeRangeComparer.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\generator\datetimerangecomparer.cs
++GithubGravatarGenerator.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\generator\githubgravatargenerator.cs
++HatchBrushGenerator.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\generator\hatchbrushgenerator.cs
++AnimationHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\animationhelper.cs
++ArithmeticHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\arithmetichelper.cs
++BindingHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\bindinghelper.cs
++ColorHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\colorhelper.cs
++ConfigHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\confighelper.cs
++DesignerHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\designerhelper.cs
++FullScreenHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\fullscreenhelper.cs
++IconHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\iconhelper.cs
++InputClickHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\inputclickhelper.cs
++ResourceHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\resourcehelper.cs
++ScreenHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\screenhelper.cs
++SecurityHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\securityhelper.cs
++SingleOpenHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\singleopenhelper.cs
++SystemHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\systemhelper.cs
++TextHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\texthelper.cs
++TokenizerHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\tokenizerhelper.cs
++TouchDragMoveWindowHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\touchdragmovewindowhelper.cs
++ValidateHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\validatehelper.cs
++VisualHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\visualhelper.cs
++WindowHelper.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\helper\windowhelper.cs
++ClipboardHook.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\hook\clipboardhook.cs
++KeyboardHook.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\hook\keyboardhook.cs
++MouseHook.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\hook\mousehook.cs
++SystemMenuHook.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\hook\systemmenuhook.cs
++Handle
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\interop\handle\
++InteropMethods.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\interop\interopmethods.cs
++InteropValues.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\interop\interopvalues.cs
++ButtonGroupItemStyleSelector.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\styleselector\buttongroupitemstyleselector.cs
++ComboBoxItemCapsuleStyleSelector.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\styleselector\comboboxitemcapsulestyleselector.cs
++TabItemCapsuleStyleSelector.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\styleselector\tabitemcapsulestyleselector.cs
++NoBlankTextRule.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\validationrule\noblanktextrule.cs
++RegexRule.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\validationrule\regexrule.cs
++BitmapHandle.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\interop\handle\bitmaphandle.cs
++CommonHandles.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\interop\handle\commonhandles.cs
++HandleCollector.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\interop\handle\handlecollector.cs
++IconHandle.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\interop\handle\iconhandle.cs
++SafeFileMappingHandle.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\interop\handle\safefilemappinghandle.cs
++WpfSafeHandle.cs
i:{************************************}:d:\projects\handycontrol-master\src\shared\handycontrol_shared\tools\interop\handle\wpfsafehandle.cs
++DemoHelper.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_code\demohelper.cs
++net40
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:>11877
++NoUserViewModel.cs
i:{d8a4748c-0500-4f63-bc47-3506658c68f7}:d:\projects\handycontrol-master\src\shared\handycontroldemo_shared\viewmodel\main\nonclientareaviewmodel.cs
++Net_40
i:{00000000-0000-0000-0000-000000000000}:Net_40
++HandyControl_Net_40
i:{35e3f9c0-8924-4536-be09-f3761fdbf066}:HandyControl_Net_40
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29816
++HandyControlDemo_Net_40
i:{35e3f9c0-8924-4536-be09-f3761fdbf066}:HandyControlDemo_Net_40
++MvvmLightLibs (5.4.1.1)
i:{d8ae88f8-c36b-4d10-a7f9-22ffcfba5231}:>29818
++Avalonia
i:{00000000-0000-0000-0000-000000000000}:Avalonia
++HandyControl_Avalonia
i:{a490c3e9-0cc2-46ee-be9a-95d3dcbf211d}:HandyControl_Avalonia
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:>29970
++Theme.axaml
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\theme.axaml
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\resources\themes\theme.axaml
++Theme.axaml.cs
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\theme.axaml.cs
++Avalonia.Analyzers
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:c:\users\<USER>\.nuget\packages\avalonia\11.0.6\analyzers\dotnet\cs\avalonia.analyzers.dll
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:c:\users\<USER>\.nuget\packages\avalonia\11.0.6\analyzers\dotnet\cs\avalonia.analyzers.dll
++Avalonia.Generators
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:c:\users\<USER>\.nuget\packages\avalonia\11.0.6\analyzers\dotnet\cs\avalonia.generators.dll
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:c:\users\<USER>\.nuget\packages\avalonia\11.0.6\analyzers\dotnet\cs\avalonia.generators.dll
++Avalonia (11.0.6)
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:>29965
++Brushes.axaml
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\basic\brushes.axaml
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\resources\themes\basic\brushes.axaml
++Colors.axaml
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\basic\colors.axaml
++Effects.axaml
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\basic\effects.axaml
++Sizes.axaml
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\basic\sizes.axaml
++Button.axaml
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\styles\button.axaml
++ContentControl.axaml
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\styles\contentcontrol.axaml
++Style.axaml
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\styles\style.axaml
++UserControl.axaml
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\styles\usercontrol.axaml
++Window.axaml
i:{34d39209-c744-41aa-91d6-c15c68e2a1d9}:d:\projects\handycontrol-master\src\avalonia\handycontrol_avalonia\themes\styles\window.axaml
++HandyControlDemo_Avalonia
i:{a490c3e9-0cc2-46ee-be9a-95d3dcbf211d}:HandyControlDemo_Avalonia
++Views
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\views\
++App.axaml
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\app.axaml
++MainWindow.axaml
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\mainwindow.axaml
++MainWindow.axaml.cs
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\mainwindow.axaml.cs
++Program.cs
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\program.cs
++App.axaml.cs
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\app.axaml.cs
++Avalonia.Desktop (11.0.6)
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:>29982
++MainWindowContent.axaml
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\views\main\mainwindowcontent.axaml
++MainWindowContent.axaml.cs
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\views\main\mainwindowcontent.axaml.cs
++ButtonDemoCtrl.axaml
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\views\styles\buttondemoctrl.axaml
++Basic.axaml
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\resources\themes\basic\basic.axaml
++ButtonDemoCtrl.axaml.cs
i:{99ccaf7c-f9a1-4c54-a5f0-b231e7f7ae66}:d:\projects\handycontrol-master\src\avalonia\handycontroldemo_avalonia\views\styles\buttondemoctrl.axaml.cs
