.links-of-author {
  margin-top: 20px;

  a, span.exturl {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
    margin-bottom: 10px;
    border-bottom-color: $black-light;
    font-size: 13px;
    if hexo-config('social_icons.transition') { the-transition(); }

    &:before {
      display: inline-block;
      vertical-align: middle;
      margin-right: 3px;
      content: " ";
      width: 4px;
      height: 4px;
      circle();
      background: rgb(random-color(0, 255) - 50%, random-color(0, 255) - 50%, random-color(0, 255) - 50%);
    }
  }
}
