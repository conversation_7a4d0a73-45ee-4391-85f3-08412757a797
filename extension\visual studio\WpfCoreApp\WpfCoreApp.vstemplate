﻿<?xml version="1.0" encoding="utf-8"?>
<VSTemplate Version="3.0.0" Type="Project" xmlns="http://schemas.microsoft.com/developer/vstemplate/2005" xmlns:sdk="http://schemas.microsoft.com/developer/vstemplate-sdkextension/2010">
    <TemplateData>
        <Name>HandyControl WPF App (.NET Core)</Name>
        <Description>Creates a HandyControl WPF App (.NET Core)</Description>
        <Icon>icon.ico</Icon>
        <ProjectType>CSharp</ProjectType>
        <CreateNewFolder>true</CreateNewFolder>
        <DefaultName>HandyControlWpfCoreApp</DefaultName>
        <ProvideDefaultName>true</ProvideDefaultName>
        <LanguageTag>csharp</LanguageTag>
        <PlatformTag>windows</PlatformTag>
        <ProjectTypeTag>desktop</ProjectTypeTag>
        <ProjectTypeTag>HandyControl</ProjectTypeTag>
        <LocationField>Enabled</LocationField>
        <EnableLocationBrowseButton>true</EnableLocationBrowseButton>
        <CreateInPlace>true</CreateInPlace>
    </TemplateData>
    <TemplateContent>
        <Project File="ProjectTemplate.csproj" ReplaceParameters="true">
            <ProjectItem ReplaceParameters="true" TargetFileName="Properties\AssemblyInfo.cs">AssemblyInfo.cs</ProjectItem>
            <ProjectItem ReplaceParameters="true">App.xaml</ProjectItem>
            <ProjectItem ReplaceParameters="true">App.xaml.cs</ProjectItem>
            <ProjectItem ReplaceParameters="true">MainWindow.xaml</ProjectItem>
            <ProjectItem ReplaceParameters="true">MainWindow.xaml.cs</ProjectItem>
        </Project>
    </TemplateContent>
</VSTemplate>
